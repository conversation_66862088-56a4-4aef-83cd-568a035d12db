
"use client";

import { useEffect, useState } from 'react';
import type { InventoryItem, Transaction } from '@/types';
import { getInventoryItems, getTransactions, addTransaction as apiAddTransaction } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { TransactionForm } from './components/transaction-form';
import { PlusCircle, ArrowUpDown, Loader2 } from 'lucide-react';
import PageHeader from '@/components/common/page-header';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { useToast } from "@/hooks/use-toast";

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const { toast } = useToast();

  async function fetchData() {
    setLoading(true);
    try {
      const [transactionsData, itemsData] = await Promise.all([
        getTransactions(),
        getInventoryItems(),
      ]);
      setTransactions(transactionsData.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
      setInventoryItems(itemsData);
    } catch (error) {
       toast({
        title: "Error fetching data",
        description: "Could not load transactions or inventory items.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchData();
  }, []);

  const handleAddTransaction = async (data: any) => { // data type from TransactionFormValues
    const newTransactionData = {
      ...data,
      date: data.date.toISOString(),
    };
    const addedTxn = await apiAddTransaction(newTransactionData);
    // Optimistically update or refetch
    setTransactions(prev => [addedTxn, ...prev].sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
    // Optionally refetch inventory items if quantities change significantly elsewhere
    // const updatedItems = await getInventoryItems();
    // setInventoryItems(updatedItems);
    setIsFormOpen(false); // Close dialog
  };

  const getTransactionTypeBadgeVariant = (type: Transaction['type']) => {
    switch (type) {
      case 'Incoming': return 'default'; // primary color
      case 'Outgoing': return 'destructive';
      case 'Consumption': return 'secondary';
      default: return 'outline';
    }
  }

  return (
    <div className="container mx-auto py-8">
      <PageHeader title="Manage Transactions" description="Record and view inventory movements.">
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" /> Add New Transaction
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>New Transaction</DialogTitle>
              <DialogDescription>
                Fill in the details to record a new inventory transaction.
              </DialogDescription>
            </DialogHeader>
            {inventoryItems.length > 0 ? (
              <TransactionForm
                inventoryItems={inventoryItems}
                onSubmit={handleAddTransaction}
                onClose={() => setIsFormOpen(false)}
              />
            ) : (
              <div className="flex items-center justify-center h-32">
                 <Loader2 className="h-8 w-8 animate-spin text-primary" />
                 <p className="ml-2">Loading inventory items...</p>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </PageHeader>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>List of the latest inventory transactions.</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : transactions.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">No transactions found.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Item Name</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead>Personnel</TableHead>
                  <TableHead>Work Order</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((txn) => (
                  <TableRow key={txn.id}>
                    <TableCell>{format(new Date(txn.date), "MMM d, yyyy")}</TableCell>
                    <TableCell>
                      <Badge variant={getTransactionTypeBadgeVariant(txn.type)}>{txn.type}</Badge>
                    </TableCell>
                    <TableCell className="font-medium">{txn.itemName}</TableCell>
                    <TableCell className="text-right">{txn.quantity}</TableCell>
                    <TableCell>{txn.personnelInvolved || '-'}</TableCell>
                    <TableCell>{txn.workOrder || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
