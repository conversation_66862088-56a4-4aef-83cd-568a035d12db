"use client";

import { useState, useEffect } from 'react';
import { inventoryService, subscribeToInventoryChanges } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';

type InventoryItem = Database['public']['Tables']['inventory_items']['Row'];
type InventoryItemInsert = Database['public']['Tables']['inventory_items']['Insert'];
type InventoryItemUpdate = Database['public']['Tables']['inventory_items']['Update'];

export function useInventory() {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load initial data
  useEffect(() => {
    loadInventory();
  }, []);

  // Subscribe to real-time changes
  useEffect(() => {
    const subscription = subscribeToInventoryChanges((payload) => {
      console.log('Inventory change:', payload);
      
      if (payload.eventType === 'INSERT') {
        setItems(prev => [...prev, payload.new]);
        toast({
          title: "تم إضافة مادة جديدة",
          description: `تم إضافة: ${payload.new.name}`,
        });
      } else if (payload.eventType === 'UPDATE') {
        setItems(prev => prev.map(item => 
          item.id === payload.new.id ? payload.new : item
        ));
        
        // Check for low stock alert
        if (payload.new.quantity <= payload.new.minimum_stock) {
          toast({
            title: "تنبيه مخزون منخفض",
            description: `${payload.new.name} وصل إلى الحد الأدنى`,
            variant: "destructive",
          });
        }
      } else if (payload.eventType === 'DELETE') {
        setItems(prev => prev.filter(item => item.id !== payload.old.id));
        toast({
          title: "تم حذف مادة",
          description: `تم حذف: ${payload.old.name}`,
        });
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [toast]);

  const loadInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await inventoryService.getAll();
      setItems(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في تحميل البيانات';
      setError(errorMessage);
      toast({
        title: "خطأ في تحميل البيانات",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const addItem = async (item: InventoryItemInsert) => {
    try {
      const newItem = await inventoryService.create(item);
      // Real-time subscription will handle the UI update
      return newItem;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في إضافة المادة';
      toast({
        title: "خطأ في إضافة المادة",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const updateItem = async (id: string, updates: InventoryItemUpdate) => {
    try {
      const updatedItem = await inventoryService.update(id, updates);
      // Real-time subscription will handle the UI update
      return updatedItem;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في تحديث المادة';
      toast({
        title: "خطأ في تحديث المادة",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const deleteItem = async (id: string) => {
    try {
      await inventoryService.delete(id);
      // Real-time subscription will handle the UI update
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في حذف المادة';
      toast({
        title: "خطأ في حذف المادة",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const getItemByBarcode = async (barcode: string) => {
    try {
      return await inventoryService.getByBarcode(barcode);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'لم يتم العثور على المادة';
      toast({
        title: "لم يتم العثور على المادة",
        description: `الباركود: ${barcode}`,
        variant: "destructive",
      });
      throw err;
    }
  };

  const getLowStockItems = async () => {
    try {
      return await inventoryService.getLowStock();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في تحميل المواد منخفضة المخزون';
      toast({
        title: "خطأ في تحميل البيانات",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  // Computed values
  const totalItems = items.length;
  const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
  const lowStockItems = items.filter(item => item.quantity <= item.minimum_stock);
  const criticalStockItems = items.filter(item => item.quantity <= Math.floor(item.minimum_stock / 2));
  const totalValue = items.reduce((sum, item) => sum + (item.quantity * (item.cost_per_unit || 0)), 0);

  const itemsByCategory = items.reduce((acc, item) => {
    acc[item.category] = (acc[item.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    // Data
    items,
    loading,
    error,
    
    // Actions
    loadInventory,
    addItem,
    updateItem,
    deleteItem,
    getItemByBarcode,
    getLowStockItems,
    
    // Computed values
    totalItems,
    totalQuantity,
    lowStockItems,
    criticalStockItems,
    totalValue,
    itemsByCategory,
  };
}

// Hook for managing transactions with Supabase
export function useTransactions() {
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      const { transactionService } = await import('@/lib/supabase');
      const data = await transactionService.getAll();
      setTransactions(data);
    } catch (err) {
      toast({
        title: "خطأ في تحميل المعاملات",
        description: err instanceof Error ? err.message : 'حدث خطأ غير متوقع',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const addTransaction = async (transaction: any) => {
    try {
      const { transactionService } = await import('@/lib/supabase');
      const newTransaction = await transactionService.create(transaction);
      setTransactions(prev => [newTransaction, ...prev]);
      
      toast({
        title: "تم تسجيل المعاملة",
        description: `تم تسجيل معاملة ${transaction.type}`,
      });
      
      return newTransaction;
    } catch (err) {
      toast({
        title: "خطأ في تسجيل المعاملة",
        description: err instanceof Error ? err.message : 'حدث خطأ غير متوقع',
        variant: "destructive",
      });
      throw err;
    }
  };

  return {
    transactions,
    loading,
    loadTransactions,
    addTransaction,
  };
}
