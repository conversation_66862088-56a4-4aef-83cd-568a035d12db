import { Cairo, Noto_Sans_Arabic } from 'next/font/google';

// Configure Cairo font
export const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  weight: ['200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-cairo',
  display: 'swap',
});

// Configure Noto Sans Arabic font
export const notoSansArabic = Noto_Sans_Arabic({
  subsets: ['arabic', 'latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-noto-sans-arabic',
  display: 'swap',
});

// Combined font classes for easy use
export const fontClasses = `${cairo.variable} ${notoSansArabic.variable}`;
