
"use client";

import { useEffect, useState } from 'react';
import type { InventoryItem } from '@/types';
import { getInventoryItems } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Package, ThermometerSnowflake, ListTree, AlertTriangle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import PageHeader from '@/components/common/page-header';
import { format } from 'date-fns';

const categoryIcons = {
  'Copper Cable': <Package className="h-5 w-5 text-primary" />,
  'FTTH Cable': <ThermometerSnowflake className="h-5 w-5 text-blue-500" />, // Using ThermometerSnowflake for FTTH/Fiber
  'Equipment': <ListTree className="h-5 w-5 text-green-500" />,
  'Other': <Package className="h-5 w-5 text-gray-500" />,
};

export default function DashboardPage() {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      const inventoryData = await getInventoryItems();
      setItems(inventoryData);
      setLoading(false);
    }
    fetchData();
  }, []);

  const totalItems = items.length;
  const lowStockItems = items.filter(item => item.quantity < 50).length; // Example threshold

  return (
    <div className="container mx-auto py-8">
      <PageHeader title="Inventory Dashboard" description="Overview of your current stock levels." />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Unique Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-1/2" /> : totalItems}</div>
            <p className="text-xs text-muted-foreground">Different types of items in stock</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quantity</CardTitle>
            <ListTree className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? <Skeleton className="h-8 w-1/2" /> : items.reduce((sum, item) => sum + item.quantity, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Total units across all items</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{loading ? <Skeleton className="h-8 w-1/2" /> : lowStockItems}</div>
            <p className="text-xs text-muted-foreground">Items needing reorder (qty &lt; 50)</p>
          </CardContent>
        </Card>
         <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <ListTree className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
                {loading ? <Skeleton className="h-8 w-1/2" /> : new Set(items.map(item => item.category)).size}
            </div>
            <p className="text-xs text-muted-foreground">Number of distinct item categories</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Current Inventory</CardTitle>
          <CardDescription>Detailed list of all items in stock.</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-10 w-full" />)}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">Icon</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Last Updated</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{categoryIcons[item.category] || <Package className="h-5 w-5 text-gray-400" />}</TableCell>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>
                      <Badge variant={item.category === 'Copper Cable' ? 'default' : item.category === 'FTTH Cable' ? 'secondary' : 'outline'}>
                        {item.category}
                      </Badge>
                    </TableCell>
                    <TableCell className={`text-right font-semibold ${item.quantity < 50 ? 'text-destructive' : ''}`}>
                      {item.quantity}
                    </TableCell>
                    <TableCell>{item.unit}</TableCell>
                    <TableCell>{format(new Date(item.lastUpdated), "PPP")}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
