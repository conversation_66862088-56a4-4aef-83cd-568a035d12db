
"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import type { Transaction, InventoryItem } from "@/types";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useTranslations, useLocale } from 'next-intl';
import { useCategoryTranslation, useTableHeaderTranslation, formatNumberArabic } from '@/lib/translations';

interface ReportDataItem {
  itemId: string;
  itemName: string;
  category: InventoryItem['category'];
  unit: string;
  incoming: number;
  outgoing: number;
  consumption: number;
  currentQuantity: number;
}

interface ReportTableProps {
  data: ReportDataItem[];
}

function convertToCSV(data: ReportDataItem[]) {
  if (data.length === 0) return "";
  const headers = Object.keys(data[0]).join(',');
  const rows = data.map(row =>
    Object.values(row).map(value =>
      typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    ).join(',')
  );
  return [headers, ...rows].join('\n');
}

function downloadCSV(csvString: string, filename: string) {
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement("a");
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

export function ReportTable({ data }: ReportTableProps) {
  const t = useTranslations('ReportTable');
  const locale = useLocale();
  const translateCategory = useCategoryTranslation();
  const translateTableHeader = useTableHeaderTranslation();

  if (data.length === 0) {
    return <p className="text-center text-muted-foreground py-8">{t('noDataAvailable')}</p>;
  }

  const handleDownload = () => {
    const csvData = convertToCSV(data);
    downloadCSV(csvData, `inventory_report_${new Date().toISOString().split('T')[0]}.csv`);
  };

  return (
    <div>
      <div className={`flex mb-4 ${locale === 'ar' ? 'justify-start' : 'justify-end'}`}>
        <Button onClick={handleDownload}>
          <Download className={`h-4 w-4 ${locale === 'ar' ? 'ml-2' : 'mr-2'}`} />
          {t('downloadCSV')}
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{translateTableHeader('itemName')}</TableHead>
            <TableHead>{translateTableHeader('category')}</TableHead>
            <TableHead className={locale === 'ar' ? 'text-left' : 'text-right'}>{t('incoming')}</TableHead>
            <TableHead className={locale === 'ar' ? 'text-left' : 'text-right'}>{t('outgoing')}</TableHead>
            <TableHead className={locale === 'ar' ? 'text-left' : 'text-right'}>{t('consumption')}</TableHead>
            <TableHead className={locale === 'ar' ? 'text-left' : 'text-right'}>{t('currentQuantity')}</TableHead>
            <TableHead>{translateTableHeader('unit')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.itemId}>
              <TableCell className="font-medium">{item.itemName}</TableCell>
              <TableCell>{translateCategory(item.category)}</TableCell>
              <TableCell className={locale === 'ar' ? 'text-left' : 'text-right'}>
                {formatNumberArabic(item.incoming, locale)}
              </TableCell>
              <TableCell className={locale === 'ar' ? 'text-left' : 'text-right'}>
                {formatNumberArabic(item.outgoing, locale)}
              </TableCell>
              <TableCell className={locale === 'ar' ? 'text-left' : 'text-right'}>
                {formatNumberArabic(item.consumption, locale)}
              </TableCell>
              <TableCell className={`${locale === 'ar' ? 'text-left' : 'text-right'} font-semibold`}>
                {formatNumberArabic(item.currentQuantity, locale)}
              </TableCell>
              <TableCell>{item.unit}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
