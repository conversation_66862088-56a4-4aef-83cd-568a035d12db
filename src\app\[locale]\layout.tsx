
import type { ReactNode } from 'react';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { AppClientLayout } from './app-client-layout'; // New client component

type Props = {
  children: ReactNode;
  params: {locale: string};
};

export default async function LocaleLayout({children, params: {locale}}: Props) {
  // Providing all messages to the client
  // side is a pragmatic approach that is easiest to get started with.
  // Messages are very cheap to passserialized to the client.
  let messages;
  try {
    messages = await getMessages();
  } catch (error) {
    console.error("Failed to get messages for locale", locale, error);
    // Fallback to an empty object or handle error appropriately
    messages = {};
  }

  return (
    // The <html> and <body> tags are handled by the root src/app/layout.tsx
    <NextIntlClientProvider locale={locale} messages={messages}>
      <AppClientLayout locale={locale}>
        {children}
      </AppClientLayout>
    </NextIntlClientProvider>
  );
}
