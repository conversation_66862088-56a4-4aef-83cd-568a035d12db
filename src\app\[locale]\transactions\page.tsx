
"use client";

import { useEffect, useState } from 'react';
import type { InventoryItem, Transaction } from '@/types';
import { getInventoryItems, getTransactions, addTransaction as apiAddTransaction } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { TransactionForm } from './components/transaction-form';
import { PlusCircle, ArrowUpDown, Loader2 } from 'lucide-react';
import PageHeader from '@/components/common/page-header';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';

export default function TransactionsPage() {
  const tPage = useTranslations('TransactionsPage');

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const { toast } = useToast();

  async function fetchData() {
    setLoading(true);
    try {
      const [transactionsData, itemsData] = await Promise.all([
        getTransactions(),
        getInventoryItems(),
      ]);
      setTransactions(transactionsData.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
      setInventoryItems(itemsData);
    } catch (error) {
       toast({
        title: tPage('toast.errorFetchingData.title'),
        description: tPage('toast.errorFetchingData.description'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchData();
  }, []); // Note: fetchData might ideally be wrapped in useCallback if it had dependencies changing often

  const handleAddTransaction = async (data: any) => { 
    const newTransactionData = {
      ...data,
      date: data.date.toISOString(),
    };
    try {
        const itemForToast = inventoryItems.find(item => item.id === data.itemId);
        const addedTxn = await apiAddTransaction(newTransactionData);
        setTransactions(prev => [addedTxn, ...prev].sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
        
        const updatedItems = await getInventoryItems(); // Refetch items to update quantities in dropdown
        setInventoryItems(updatedItems);
        setIsFormOpen(false); 
        toast({
            title: tPage('toast.transactionAdded.title'),
            description: tPage('toast.transactionAdded.description', { itemName: itemForToast?.name || data.itemId }),
          });
    } catch (error) {
        toast({
            title: tPage('toast.errorAddingTransaction.title'),
            description: tPage('toast.errorAddingTransaction.description', { details: (error instanceof Error ? ` ${error.message}` : '') }),
            variant: "destructive",
          });
    }
  };

  const getTransactionTypeBadgeVariant = (type: Transaction['type']) => {
    switch (type) {
      case 'Incoming': return 'default';
      case 'Outgoing': return 'destructive';
      case 'Consumption': return 'secondary';
      default: return 'outline';
    }
  }

  return (
    <div className="container mx-auto py-8">
      <PageHeader title={tPage('title')} description={tPage('description')}>
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" /> {tPage('addNewTransactionButton')}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{tPage('dialogAddTitle')}</DialogTitle>
              <DialogDescription>
                {tPage('dialogAddDescription')}
              </DialogDescription>
            </DialogHeader>
            {inventoryItems.length > 0 ? (
              <TransactionForm
                inventoryItems={inventoryItems}
                onSubmit={handleAddTransaction}
                onClose={() => setIsFormOpen(false)}
              />
            ) : (
              <div className="flex items-center justify-center h-32">
                 <Loader2 className="h-8 w-8 animate-spin text-primary" />
                 <p className="ml-2 rtl:ml-0 rtl:mr-2">{tPage('loadingItems')}</p>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </PageHeader>

      <Card>
        <CardHeader>
          <CardTitle>{tPage('recentTransactionsTitle')}</CardTitle>
          <CardDescription>{tPage('recentTransactionsDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : transactions.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">{tPage('noTransactionsFound')}</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow><TableHead>{tPage('tableDate')}</TableHead><TableHead>{tPage('tableType')}</TableHead><TableHead>{tPage('tableItemName')}</TableHead><TableHead className="text-right rtl:text-left">{tPage('tableQuantity')}</TableHead><TableHead>{tPage('tablePersonnel')}</TableHead><TableHead>{tPage('tableWorkOrder')}</TableHead></TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((txn) => (
                  <TableRow key={txn.id}><TableCell>{format(new Date(txn.date), "MMM d, yyyy")}</TableCell><TableCell><Badge variant={getTransactionTypeBadgeVariant(txn.type)}>{txn.type}</Badge></TableCell><TableCell className="font-medium">{txn.itemName}</TableCell><TableCell className="text-right rtl:text-left">{txn.quantity}</TableCell><TableCell>{txn.personnelInvolved || '-'}</TableCell><TableCell>{txn.workOrder || '-'}</TableCell></TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
