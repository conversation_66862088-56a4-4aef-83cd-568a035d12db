# دليل إعداد Supabase لنظام Copper Flow

## 🚀 **خطوات الإعداد:**

### 1. **إنشاء مشروع Supabase**

1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل الدخول
3. انقر على "New Project"
4. اختر اسم المشروع: `copper-flow`
5. اختر كلمة مرور قوية لقاعدة البيانات
6. اختر المنطقة الأقرب لك

### 2. **الحصول على مفاتيح API**

1. اذهب إلى Settings > API
2. انسخ:
   - `Project URL`
   - `anon public key`
   - `service_role key` (احتفظ بها آمنة)

### 3. **إعداد متغيرات البيئة**

```bash
# انسخ ملف البيئة المثال
cp .env.example .env.local

# أضف مفاتيح Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 4. **تثبيت مكتبات Supabase**

```bash
npm install @supabase/supabase-js
npm install @supabase/auth-helpers-nextjs
npm install @supabase/auth-ui-react
npm install @supabase/auth-ui-shared
```

### 5. **إنشاء قاعدة البيانات**

1. اذهب إلى SQL Editor في Supabase Dashboard
2. انسخ محتوى ملف `supabase/migrations/001_initial_schema.sql`
3. شغل الاستعلام لإنشاء الجداول

### 6. **إعداد المصادقة**

1. اذهب إلى Authentication > Settings
2. فعل Email confirmation إذا أردت
3. أضف Site URL: `http://localhost:9002`
4. أضف Redirect URLs:
   - `http://localhost:9002/auth/callback`
   - `https://yourdomain.com/auth/callback` (للإنتاج)

### 7. **إعداد Row Level Security**

الجداول محمية بـ RLS policies التي تم إنشاؤها في الـ migration.

### 8. **إدخال بيانات تجريبية**

```sql
-- إدخال مستخدم تجريبي (بعد التسجيل)
INSERT INTO users (id, email, name, role) 
VALUES (auth.uid(), '<EMAIL>', 'مدير النظام', 'admin');

-- إدخال موردين تجريبيين
INSERT INTO suppliers (name, contact_person, email, phone, address, city, country, currency) VALUES
('شركة النحاس المتقدم', 'أحمد محمد', '<EMAIL>', '+************', 'شارع الملك فهد', 'الرياض', 'السعودية', 'SAR'),
('مؤسسة الألياف البصرية', 'فاطمة علي', '<EMAIL>', '+************', 'طريق الملك عبدالعزيز', 'جدة', 'السعودية', 'SAR');

-- إدخال مواد تجريبية
INSERT INTO inventory_items (name, category, quantity, unit, minimum_stock, cost_per_unit) VALUES
('كابل نحاس 2 زوج', 'Copper Cable', 1000, 'متر', 100, 5.50),
('كابل ألياف بصرية 12 core', 'FTTH Cable', 500, 'متر', 50, 25.00),
('موصل RJ45', 'Equipment', 200, 'قطعة', 20, 2.00),
('صندوق توزيع', 'Equipment', 50, 'قطعة', 10, 150.00);
```

## 🔧 **الميزات المتاحة:**

### **Real-time Updates:**
- تحديثات فورية للمخزون
- إشعارات المخزون المنخفض
- مزامنة البيانات عبر الأجهزة

### **الأمان:**
- Row Level Security
- أدوار المستخدمين (admin, manager, employee)
- تشفير البيانات

### **الأداء:**
- فهرسة محسنة
- استعلامات SQL سريعة
- تخزين مؤقت تلقائي

## 📊 **مقارنة التكلفة:**

### **Supabase:**
- مجاني حتى 500MB قاعدة بيانات
- $25/شهر للخطة Pro
- $599/شهر للخطة Team

### **Firebase:**
- مجاني حتى حدود معينة
- $25/شهر للخطة Blaze (حسب الاستخدام)
- تكلفة أعلى مع النمو

## 🚀 **خطوات التشغيل:**

```bash
# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm run dev
```

## 🔄 **Migration من البيانات الوهمية:**

إذا كنت تريد نقل البيانات الحالية:

```typescript
// src/scripts/migrate-to-supabase.ts
import { mockInventoryItems, mockTransactions } from '@/lib/data';
import { inventoryService, transactionService } from '@/lib/supabase';

async function migrateData() {
  // نقل المواد
  for (const item of mockInventoryItems) {
    await inventoryService.create({
      name: item.name,
      category: item.category,
      quantity: item.quantity,
      unit: item.unit,
      minimum_stock: 50, // قيمة افتراضية
    });
  }

  // نقل المعاملات
  for (const transaction of mockTransactions) {
    await transactionService.create({
      type: transaction.type,
      item_id: transaction.itemId,
      quantity: transaction.quantity,
      date: transaction.date,
      description: transaction.description,
      reason: transaction.reason,
      work_order: transaction.workOrder,
      personnel_involved: transaction.personnelInvolved,
      location: transaction.location,
    });
  }
}
```

## 🛠 **استكشاف الأخطاء:**

### **خطأ في الاتصال:**
- تأكد من صحة URL و API keys
- تحقق من إعدادات CORS

### **خطأ في الصلاحيات:**
- تأكد من تفعيل RLS policies
- تحقق من دور المستخدم

### **خطأ في البيانات:**
- تحقق من صحة schema
- راجع قيود الجداول

## 📚 **موارد إضافية:**

- [Supabase Documentation](https://supabase.com/docs)
- [Next.js + Supabase Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-nextjs)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
