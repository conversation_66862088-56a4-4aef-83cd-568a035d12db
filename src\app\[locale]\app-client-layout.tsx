
"use client";

import type { ReactNode } from 'react';
import { SidebarProvider } from "@/components/ui/sidebar";
import { MainSidebar } from "@/components/layout/main-sidebar";
import { MainHeader } from "@/components/layout/main-header";
import LocaleHandler from '@/components/common/locale-handler';
import { AuthProvider, useAuth } from '@/contexts/auth-context';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

interface AppClientLayoutProps {
  children: ReactNode;
  locale: string;
}

function AuthenticatedAppContent({ children, locale }: AppClientLayoutProps) {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const pathname = usePathname();
  const router = useRouter();

  const isLoginPage = pathname === `/${locale}/login`;
  const isUserManagementPage = pathname === `/${locale}/users`;

  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated && !isLoginPage) {
        router.push(`/${locale}/login`);
      } else if (isAuthenticated && isLoginPage) {
        router.push(`/${locale}/`);
      } else if (isAuthenticated && isUserManagementPage && user?.role !== 'admin') {
        // If authenticated, on user management page, but not admin, redirect to dashboard
        router.push(`/${locale}/`);
      }
    }
  }, [isAuthenticated, authLoading, isLoginPage, isUserManagementPage, user, router, locale, pathname]);

  if (authLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
      </div>
    );
  }

  if (!isAuthenticated && !isLoginPage) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
      </div>
    );
  }

  if (isLoginPage) {
    return <>{children}</>;
  }

  // If trying to access user management without admin role (and not loading)
  if (isUserManagementPage && (!user || user.role !== 'admin')) {
    return ( // Show a loading or unauthorized message while redirecting
      <div className="flex min-h-screen items-center justify-center bg-background">
        <p>Unauthorized access. Redirecting...</p>
        <Loader2 className="ml-2 h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }


  if (isAuthenticated) {
    return (
      <SidebarProvider defaultOpen={true}>
        <div className="flex min-h-screen">
          {/* Sidebar positioning based on locale */}
          <div className={`${locale === 'ar' ? 'order-2' : 'order-1'}`}>
            <MainSidebar />
          </div>
          <div className={`flex flex-col flex-1 min-h-screen ${locale === 'ar' ? 'order-1' : 'order-2'}`}>
            <MainHeader />
            <main className="flex-1 p-4 md:p-6 lg:p-8 bg-background arabic-text">
              {children}
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  // Fallback for any other case (e.g. not authenticated and not login page, should be handled by redirect)
  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <Loader2 className="h-16 w-16 animate-spin text-primary" />
    </div>
  );
}

export function AppClientLayout({ children, locale }: AppClientLayoutProps) {
  return (
    <AuthProvider>
      <LocaleHandler locale={locale}>
        <AuthenticatedAppContent locale={locale}>
          {children}
        </AuthenticatedAppContent>
      </LocaleHandler>
    </AuthProvider>
  );
}
