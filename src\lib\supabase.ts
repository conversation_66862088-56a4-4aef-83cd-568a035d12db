import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Database {
  public: {
    Tables: {
      inventory_items: {
        Row: {
          id: string
          name: string
          category: 'Copper Cable' | 'FTTH Cable' | 'Equipment' | 'Other'
          quantity: number
          unit: string
          minimum_stock: number
          barcode?: string
          location?: string
          cost_per_unit?: number
          supplier_id?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          category: 'Copper Cable' | 'FTTH Cable' | 'Equipment' | 'Other'
          quantity: number
          unit: string
          minimum_stock?: number
          barcode?: string
          location?: string
          cost_per_unit?: number
          supplier_id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          category?: 'Copper Cable' | 'FTTH Cable' | 'Equipment' | 'Other'
          quantity?: number
          unit?: string
          minimum_stock?: number
          barcode?: string
          location?: string
          cost_per_unit?: number
          supplier_id?: string
          updated_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          type: 'Incoming' | 'Outgoing' | 'Consumption'
          item_id: string
          quantity: number
          date: string
          description?: string
          reason?: string
          work_order?: string
          personnel_involved?: string
          location?: string
          cost_per_unit?: number
          total_cost?: number
          created_by?: string
          created_at: string
        }
        Insert: {
          id?: string
          type: 'Incoming' | 'Outgoing' | 'Consumption'
          item_id: string
          quantity: number
          date: string
          description?: string
          reason?: string
          work_order?: string
          personnel_involved?: string
          location?: string
          cost_per_unit?: number
          total_cost?: number
          created_by?: string
          created_at?: string
        }
        Update: {
          id?: string
          type?: 'Incoming' | 'Outgoing' | 'Consumption'
          item_id?: string
          quantity?: number
          date?: string
          description?: string
          reason?: string
          work_order?: string
          personnel_involved?: string
          location?: string
          cost_per_unit?: number
          total_cost?: number
        }
      }
      suppliers: {
        Row: {
          id: string
          name: string
          contact_person: string
          email: string
          phone: string
          address: string
          city: string
          country: string
          rating: number
          status: 'Active' | 'Inactive' | 'Pending'
          payment_terms?: string
          delivery_time?: number
          minimum_order?: number
          currency: string
          tax_id?: string
          website?: string
          notes?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          contact_person: string
          email: string
          phone: string
          address: string
          city: string
          country: string
          rating?: number
          status?: 'Active' | 'Inactive' | 'Pending'
          payment_terms?: string
          delivery_time?: number
          minimum_order?: number
          currency?: string
          tax_id?: string
          website?: string
          notes?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          contact_person?: string
          email?: string
          phone?: string
          address?: string
          city?: string
          country?: string
          rating?: number
          status?: 'Active' | 'Inactive' | 'Pending'
          payment_terms?: string
          delivery_time?: number
          minimum_order?: number
          currency?: string
          tax_id?: string
          website?: string
          notes?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          name?: string
          role: 'admin' | 'manager' | 'employee'
          avatar_url?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name?: string
          role?: 'admin' | 'manager' | 'employee'
          avatar_url?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'admin' | 'manager' | 'employee'
          avatar_url?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Helper functions for common operations
export const inventoryService = {
  // Get all inventory items
  async getAll() {
    const { data, error } = await supabase
      .from('inventory_items')
      .select('*')
      .order('name')
    
    if (error) throw error
    return data
  },

  // Get item by ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('inventory_items')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Get item by barcode
  async getByBarcode(barcode: string) {
    const { data, error } = await supabase
      .from('inventory_items')
      .select('*')
      .eq('barcode', barcode)
      .single()
    
    if (error) throw error
    return data
  },

  // Get low stock items
  async getLowStock() {
    const { data, error } = await supabase
      .from('inventory_items')
      .select('*')
      .lt('quantity', supabase.raw('minimum_stock'))
    
    if (error) throw error
    return data
  },

  // Add new item
  async create(item: Database['public']['Tables']['inventory_items']['Insert']) {
    const { data, error } = await supabase
      .from('inventory_items')
      .insert(item)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update item
  async update(id: string, updates: Database['public']['Tables']['inventory_items']['Update']) {
    const { data, error } = await supabase
      .from('inventory_items')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete item
  async delete(id: string) {
    const { error } = await supabase
      .from('inventory_items')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

export const transactionService = {
  // Get all transactions
  async getAll() {
    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        inventory_items (name, category, unit)
      `)
      .order('date', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Add new transaction
  async create(transaction: Database['public']['Tables']['transactions']['Insert']) {
    const { data, error } = await supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single()
    
    if (error) throw error

    // Update inventory quantity
    await this.updateInventoryQuantity(transaction.item_id, transaction.type, transaction.quantity)
    
    return data
  },

  // Update inventory quantity based on transaction
  async updateInventoryQuantity(itemId: string, type: string, quantity: number) {
    const { data: item } = await supabase
      .from('inventory_items')
      .select('quantity')
      .eq('id', itemId)
      .single()

    if (!item) throw new Error('Item not found')

    let newQuantity = item.quantity
    if (type === 'Incoming') {
      newQuantity += quantity
    } else if (type === 'Outgoing' || type === 'Consumption') {
      newQuantity -= quantity
    }

    if (newQuantity < 0) {
      throw new Error('Insufficient stock')
    }

    const { error } = await supabase
      .from('inventory_items')
      .update({ 
        quantity: newQuantity,
        updated_at: new Date().toISOString()
      })
      .eq('id', itemId)

    if (error) throw error
  }
}

// Real-time subscriptions
export const subscribeToInventoryChanges = (callback: (payload: any) => void) => {
  return supabase
    .channel('inventory_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'inventory_items' }, 
      callback
    )
    .subscribe()
}

export const subscribeToTransactionChanges = (callback: (payload: any) => void) => {
  return supabase
    .channel('transaction_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'transactions' }, 
      callback
    )
    .subscribe()
}
