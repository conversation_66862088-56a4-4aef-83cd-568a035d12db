
import type { InventoryItem, Transaction, User, UserRole } from '@/types';
import type { ItemFormValues } from '@/app/[locale]/items/components/item-form'; 

export const mockInventoryItems: InventoryItem[] = [
  { id: 'item-1', name: 'Copper Cable 2-pair', quantity: 1500, category: 'Copper Cable', unit: 'meters', lastUpdated: "2023-10-25T10:00:00.000Z" },
  { id: 'item-2', name: 'FTTH Drop Cable 1-core', quantity: 3000, category: 'FTTH Cable', unit: 'meters', lastUpdated: "2023-10-24T11:30:00.000Z" },
  { id: 'item-3', name: 'Splice Closure', quantity: 50, category: 'Equipment', unit: 'units', lastUpdated: "2023-10-26T08:15:00.000Z" },
  { id: 'item-4', name: 'RJ45 Connectors', quantity: 500, category: 'Other', unit: 'units', lastUpdated: "2023-10-23T14:45:00.000Z" },
  { id: 'item-5', name: 'Patch Panel 24-port', quantity: 20, category: 'Equipment', unit: 'units', lastUpdated: "2023-10-27T09:00:00.000Z" },
];

export const mockTransactions: Transaction[] = [
  {
    id: 'txn-1', type: 'Incoming', itemId: 'item-1', itemName: 'Copper Cable 2-pair', quantity: 500, date: "2023-10-22T10:00:00.000Z",
    personnelInvolved: 'Alice Smith', workOrder: 'WO-001', reason: 'Stock Replenishment', location: 'Main Warehouse'
  },
  {
    id: 'txn-2', type: 'Outgoing', itemId: 'item-2', itemName: 'FTTH Drop Cable 1-core', quantity: 200, date: "2023-10-24T12:00:00.000Z",
    personnelInvolved: 'Bob Johnson', workOrder: 'WO-002', reason: 'Project Installation A', location: 'Site A'
  },
  {
    id: 'txn-3', type: 'Consumption', itemId: 'item-3', itemName: 'Splice Closure', quantity: 5, date: "2023-10-25T15:30:00.000Z",
    personnelInvolved: 'Charlie Brown', workOrder: 'WO-003', reason: 'Maintenance Task', location: 'Site B'
  },
  {
    id: 'txn-4', type: 'Incoming', itemId: 'item-4', itemName: 'RJ45 Connectors', quantity: 200, date: "2023-10-26T11:00:00.000Z",
    personnelInvolved: 'Alice Smith', workOrder: 'WO-004', reason: 'New Stock', location: 'Main Warehouse'
  },
];

export let mockAppUsers: User[] = [
  { id: 'admin-001', username: 'admin', email: '<EMAIL>', name: 'Administrator', role: 'admin' },
  { id: 'supervisor-001', username: 'supervisor', email: '<EMAIL>', name: 'Supervisor One', role: 'supervisor' },
  { id: 'user-001', username: 'user1', email: '<EMAIL>', name: 'Regular User One', role: 'user' },
  { id: 'user-002', username: 'user2', email: '<EMAIL>', name: 'Regular User Two', role: 'user' },
];


// Simulate API calls
export const getInventoryItems = async (): Promise<InventoryItem[]> => {
  return new Promise(resolve => setTimeout(() => resolve(JSON.parse(JSON.stringify(mockInventoryItems))), 500));
};

export const getTransactions = async (): Promise<Transaction[]> => {
  return new Promise(resolve => setTimeout(() => resolve(JSON.parse(JSON.stringify(mockTransactions))), 500)); 
};

export const addTransaction = async (transaction: Omit<Transaction, 'id' | 'itemName'>): Promise<Transaction> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const itemIndex = mockInventoryItems.findIndex(i => i.id === transaction.itemId);
      if (itemIndex === -1) {
        reject(new Error('Item not found for transaction'));
        return;
      }
      const item = mockInventoryItems[itemIndex];

      const newTransaction: Transaction = {
        ...transaction,
        id: `txn-${Date.now()}-${Math.random().toString(36).substring(2,7)}`,
        itemName: item.name,
        date: typeof transaction.date === 'string' ? transaction.date : (transaction.date as Date).toISOString(),
      };
      mockTransactions.unshift(newTransaction); 

      if (transaction.type === 'Incoming') {
        mockInventoryItems[itemIndex].quantity += transaction.quantity;
      } else if (transaction.type === 'Outgoing' || transaction.type === 'Consumption') {
        if (mockInventoryItems[itemIndex].quantity < transaction.quantity) {
          reject(new Error(`Not enough stock for ${item.name}. Available: ${item.quantity}, Tried to use: ${transaction.quantity}`));
          mockTransactions.shift(); 
          return;
        }
        mockInventoryItems[itemIndex].quantity -= transaction.quantity;
      }
      mockInventoryItems[itemIndex].lastUpdated = new Date().toISOString();
      resolve(JSON.parse(JSON.stringify(newTransaction)));
    }, 500);
  });
};

export const addInventoryItem = async (itemData: ItemFormValues): Promise<InventoryItem> => {
  return new Promise(resolve => {
    setTimeout(() => {
      const newItem: InventoryItem = {
        ...itemData,
        id: `item-${Date.now()}-${Math.random().toString(36).substring(2,7)}`,
        lastUpdated: new Date().toISOString(),
      };
      mockInventoryItems.push(newItem);
      resolve(JSON.parse(JSON.stringify(newItem)));
    }, 300);
  });
};

export const updateInventoryItem = async (itemId: string, itemData: ItemFormValues): Promise<InventoryItem> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const itemIndex = mockInventoryItems.findIndex(i => i.id === itemId);
      if (itemIndex === -1) {
        reject(new Error('Item not found for update'));
        return;
      }
      mockInventoryItems[itemIndex] = {
        ...mockInventoryItems[itemIndex],
        ...itemData,
        lastUpdated: new Date().toISOString(),
      };
      resolve(JSON.parse(JSON.stringify(mockInventoryItems[itemIndex])));
    }, 300);
  });
};

export const deleteInventoryItem = async (itemId: string): Promise<{ id: string }> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const itemIndex = mockInventoryItems.findIndex(i => i.id === itemId);
      if (itemIndex === -1) {
        reject(new Error('Item not found for deletion'));
        return;
      }
      
      const isUsedInTransaction = mockTransactions.some(t => t.itemId === itemId);
      if (isUsedInTransaction) {
        reject(new Error('Cannot delete item. It is used in existing transactions.'));
        return;
      }
      mockInventoryItems.splice(itemIndex, 1);
      resolve({ id: itemId });
    }, 300);
  });
};

// User Management Mock Functions
export const getAppUsers = async (): Promise<User[]> => {
  return new Promise(resolve => setTimeout(() => resolve(JSON.parse(JSON.stringify(mockAppUsers))), 200));
};

export const updateAppUserRole = async (userId: string, newRole: UserRole): Promise<User | null> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const userIndex = mockAppUsers.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        mockAppUsers[userIndex].role = newRole;
        resolve(JSON.parse(JSON.stringify(mockAppUsers[userIndex])));
      } else {
        reject(new Error('User not found for role update.'));
      }
    }, 200);
  });
};
