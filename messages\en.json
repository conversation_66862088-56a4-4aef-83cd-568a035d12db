{"Navigation": {"dashboard": "Dashboard", "transactions": "Transactions", "reports": "Reports", "manageItems": "Manage Items", "copperFlow": "Copper Flow", "userManagement": "User Management"}, "DashboardPage": {"title": "Inventory Dashboard", "description": "Overview of your current stock levels.", "totalUniqueItems": "Total Unique Items", "totalUniqueItemsDesc": "Different types of items in stock", "totalQuantity": "Total Quantity", "totalQuantityDesc": "Total units across all items", "lowStockItems": "Low Stock Items", "lowStockItemsDesc": "Items needing reorder (qty < 50)", "categories": "Categories", "categoriesDesc": "Number of distinct item categories", "currentInventoryTitle": "Current Inventory", "currentInventoryDescription": "Detailed list of all items in stock."}, "ItemsPage": {"title": "Manage Inventory Items", "description": "Add, edit, or remove items from your inventory.", "addNewItemButton": "Add New Item", "dialogEditTitle": "<PERSON>em", "dialogAddTitle": "Add New Item", "dialogEditDescription": "Update the details of this inventory item.", "dialogAddDescription": "Fill in the details for the new inventory item.", "alertDeleteTitle": "Are you sure?", "alertDeleteDescription": "This action cannot be undone. This will permanently delete the item \"{itemName}\".", "inventoryListTitle": "Inventory List", "inventoryListDescription": "All items currently in your inventory.", "tableIcon": "Icon", "tableName": "Name", "tableCategory": "Category", "tableQuantity": "Quantity", "tableUnit": "Unit", "tableLastUpdated": "Last Updated", "tableActions": "Actions", "noItemsFound": "No items found. Click \"Add New Item\" to get started.", "editSrOnly": "Edit {itemName}", "deleteSrOnly": "Delete {itemName}", "toast": {"itemUpdated": {"title": "Item <PERSON>", "description": "{itemName} has been updated."}, "itemAdded": {"title": "<PERSON>em Added", "description": "{itemName} has been added."}, "itemDeleted": {"title": "Item Deleted", "description": "{itemName} has been deleted."}, "errorFetching": {"title": "Error fetching items", "description": "Could not load inventory items."}, "errorSaving": {"title": "Error saving item", "description": "Could not save item."}, "errorDeleting": {"title": "Error deleting item", "description": "Could not delete item."}}}, "ItemForm": {"itemNameLabel": "Item Name", "itemNamePlaceholder": "e.g., Copper Cable 2-pair", "categoryLabel": "Category", "selectCategoryPlaceholder": "Select a category", "unitLabel": "Unit", "unitPlaceholder": "e.g., meters, units, rolls", "quantityLabel": "Quantity", "quantityPlaceholder": "Enter current stock quantity", "cancelButton": "Cancel", "savingButton": "Saving...", "saveChangesButton": "Save Changes", "addItemButton": "Add Item"}, "MainHeader": {"toggleNavigationMenu": "Toggle navigation menu", "toggleSidebar": "Toggle sidebar", "userMenu": "User menu", "language": "Language", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "Common": {"delete": "Delete", "cancel": "Cancel"}, "TransactionsPage": {"title": "Manage Transactions", "description": "Record and view inventory movements.", "addNewTransactionButton": "Add New Transaction", "dialogAddTitle": "New Transaction", "dialogAddDescription": "Fill in the details to record a new inventory transaction.", "loadingItems": "Loading inventory items...", "recentTransactionsTitle": "Recent Transactions", "recentTransactionsDescription": "List of the latest inventory transactions.", "noTransactionsFound": "No transactions found.", "tableDate": "Date", "tableType": "Type", "tableItemName": "Item Name", "tableQuantity": "Quantity", "tablePersonnel": "Personnel", "tableWorkOrder": "Work Order", "toast": {"errorFetchingData": {"title": "Error Fetching Data", "description": "Could not load transactions or inventory items."}, "transactionAdded": {"title": "Transaction Added", "description": "Transaction for {itemName} has been recorded."}, "errorAddingTransaction": {"title": "Error Adding Transaction", "description": "Failed to add transaction.{details}"}}}, "TransactionForm": {"transactionTypeLabel": "Transaction Type", "transactionTypePlaceholder": "Select transaction type", "materialItemLabel": "Material/Item", "materialItemPlaceholder": "Select an item", "quantityLabel": "Quantity", "quantityPlaceholder": "Enter quantity", "dateLabel": "Date", "datePickerPlaceholder": "Pick a date", "descriptionLabel": "Description (Optional)", "descriptionPlaceholder": "Brief description of the transaction", "reasonLabel": "Reason (Optional)", "reasonPlaceholder": "Reason for transaction", "workOrderLabel": "Work Order (Optional)", "workOrderPlaceholder": "e.g., WO-12345", "personnelInvolvedLabel": "Personnel Involved (Optional)", "personnelInvolvedPlaceholder": "Name of person(s)", "locationLabel": "Location (Optional)", "locationPlaceholder": "e.g., Main Warehouse, Site A", "cancelButton": "Cancel", "submittingButton": "Submitting...", "submitButton": "Submit Transaction", "toastSuccessTitle": "Transaction Successful", "toastSuccessDescription": "Transaction for {itemName} recorded.", "toastErrorTitle": "Error", "toastErrorDescription": "Failed to record transaction.{details}"}, "ReportsPage": {"title": "Inventory Reports", "description": "Analyze inventory movements based on filters.", "summaryReportCard": {"title": "Report Summary", "description": "Inventory summary based on the applied filters."}, "detailedTransactionReportCard": {"title": "Detailed Transaction Log", "description": "Complete log of all transactions matching the current filters."}, "toast": {"errorFetchingData": {"title": "Error Fetching Data", "description": "Could not load initial data for reports."}}}, "DetailedTransactionReport": {"noTransactionsFound": "No transactions found for the selected filters.", "downloadCsvButton": "Download CSV", "tableDate": "Date", "tableType": "Type", "tableItemName": "Item Name", "tableItemCategory": "Category", "tableQuantity": "Quantity", "tableItemUnit": "Unit", "tablePersonnel": "Personnel", "tableWorkOrder": "Work Order", "tableLocation": "Location", "tableDescription": "Description", "tableReason": "Reason"}, "LoginPage": {"title": "Login - Copper Flow", "formTitle": "Welcome Back!", "formDescription": "Enter your credentials to access your inventory.", "usernameLabel": "Username", "usernamePlaceholder": "Enter your username", "passwordLabel": "Password", "passwordPlaceholder": "••••••••", "loginButton": "<PERSON><PERSON>", "loggingInButton": "Logging In..."}, "UserManagementPage": {"title": "User Management", "description": "View users and manage their roles within the application.", "userListTitle": "User List", "userListDescription": "All registered users in the system.", "tableIcon": "Icon", "tableName": "Name", "tableEmail": "Email", "tableRole": "Role", "tableActions": "Change Role", "noUsersFound": "No users found in the system.", "selectRolePlaceholder": "Select new role", "unauthorizedAccess": "You are not authorized to view this page.", "toast": {"errorFetching": {"title": "Error Fetching Users", "description": "Could not load user data."}, "roleUpdated": {"title": "Role Updated", "description": "Role for {userName} has been updated to {role}."}, "errorUpdatingRole": {"title": "Error Updating Role", "description": "Could not update user role."}}}, "UserRoles": {"admin": "Admin", "supervisor": "Supervisor", "user": "User"}}