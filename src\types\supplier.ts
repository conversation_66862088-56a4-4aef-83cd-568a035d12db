export interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  rating: number; // 1-5 stars
  status: 'Active' | 'Inactive' | 'Pending';
  paymentTerms: string;
  deliveryTime: number; // in days
  minimumOrder: number;
  currency: string;
  taxId?: string;
  website?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseOrder {
  id: string;
  supplierId: string;
  supplierName: string;
  orderNumber: string;
  status: 'Draft' | 'Sent' | 'Confirmed' | 'Delivered' | 'Cancelled';
  orderDate: string;
  expectedDeliveryDate: string;
  actualDeliveryDate?: string;
  items: PurchaseOrderItem[];
  subtotal: number;
  tax: number;
  total: number;
  currency: string;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseOrderItem {
  id: string;
  itemId: string;
  itemName: string;
  quantity: number;
  unitPrice: number;
  total: number;
  unit: string;
  description?: string;
}

export interface SupplierRating {
  id: string;
  supplierId: string;
  orderId: string;
  rating: number; // 1-5
  qualityRating: number; // 1-5
  deliveryRating: number; // 1-5
  serviceRating: number; // 1-5
  comments?: string;
  ratedBy: string;
  ratedAt: string;
}

export interface SupplierPerformance {
  supplierId: string;
  totalOrders: number;
  onTimeDeliveries: number;
  lateDeliveries: number;
  cancelledOrders: number;
  averageRating: number;
  averageDeliveryTime: number;
  totalValue: number;
  lastOrderDate?: string;
}
