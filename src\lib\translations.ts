import { useTranslations } from 'next-intl';

// Hook for translating categories
export function useCategoryTranslation() {
  const t = useTranslations('Categories');

  return (category: string) => {
    return t(category as any) || category;
  };
}

// Hook for translating transaction types
export function useTransactionTypeTranslation() {
  const t = useTranslations('TransactionTypes');

  return (type: string) => {
    return t(type as any) || type;
  };
}

// Hook for translating units
export function useUnitTranslation() {
  const t = useTranslations('Units');

  return (unit: string) => {
    return t(unit as any) || unit;
  };
}

// Hook for translating table headers
export function useTableHeaderTranslation() {
  const t = useTranslations('TableHeaders');

  return (header: string) => {
    return t(header as any) || header;
  };
}

// Hook for translating actions
export function useActionTranslation() {
  const t = useTranslations('Actions');

  return (action: string) => {
    return t(action as any) || action;
  };
}

// Hook for translating user roles
export function useUserRoleTranslation() {
  const t = useTranslations('UserRoles');

  return (role: string) => {
    return t(role as any) || role;
  };
}

// Utility function to format dates in Arabic (Gregorian calendar)
export function formatDateArabic(date: string | Date, locale: string = 'ar') {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (locale === 'ar') {
    // Use Arabic locale but with Gregorian calendar (default)
    return new Intl.DateTimeFormat('ar-EG', {
      calendar: 'gregory', // Explicitly use Gregorian calendar
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true // Use 12-hour format with AM/PM in Arabic
    }).format(dateObj);
  }

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj);
}

// Additional date formatting functions
export function formatDateShort(date: string | Date, locale: string = 'ar') {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (locale === 'ar') {
    return new Intl.DateTimeFormat('ar-EG', {
      calendar: 'gregory',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(dateObj);
  }

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(dateObj);
}

export function formatDateLong(date: string | Date, locale: string = 'ar') {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (locale === 'ar') {
    return new Intl.DateTimeFormat('ar-EG', {
      calendar: 'gregory',
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  }

  return new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(dateObj);
}

export function formatTimeOnly(date: string | Date, locale: string = 'ar') {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (locale === 'ar') {
    return new Intl.DateTimeFormat('ar-EG', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(dateObj);
  }

  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj);
}

// Utility function to format numbers in Arabic
export function formatNumberArabic(number: number, locale: string = 'ar') {
  if (locale === 'ar') {
    return new Intl.NumberFormat('ar-EG').format(number);
  }

  return new Intl.NumberFormat('en-US').format(number);
}

// Categories mapping for consistency
export const CATEGORIES = {
  'Copper Cable': 'Copper Cable',
  'FTTH Cable': 'FTTH Cable',
  'Equipment': 'Equipment',
  'Other': 'Other'
} as const;

// Transaction types mapping
export const TRANSACTION_TYPES = {
  'Incoming': 'Incoming',
  'Outgoing': 'Outgoing',
  'Consumption': 'Consumption'
} as const;

// Common units mapping
export const UNITS = {
  'meter': 'meter',
  'piece': 'piece',
  'roll': 'roll',
  'box': 'box',
  'kg': 'kg',
  'unit': 'unit'
} as const;
