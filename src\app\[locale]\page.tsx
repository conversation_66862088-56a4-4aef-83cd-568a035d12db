
"use client";

import { useEffect, useState } from 'react';
import type { InventoryItem } from '@/types';
import { getInventoryItems } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Package, ThermometerSnowflake, ListTree, AlertTriangle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import PageHeader from '@/components/common/page-header';
import { format } from 'date-fns';
import { useTranslations, useLocale } from 'next-intl';
import { useCategoryTranslation, useTableHeaderTranslation, formatDateArabic, formatNumberArabic } from '@/lib/translations';

const categoryIcons = {
  'Copper Cable': <Package className="h-5 w-5 text-primary" />,
  'FTTH Cable': <ThermometerSnowflake className="h-5 w-5 text-blue-500" />,
  'Equipment': <ListTree className="h-5 w-5 text-green-500" />,
  'Other': <Package className="h-5 w-5 text-gray-500" />,
};

export default function DashboardPage() {
  const t = useTranslations('DashboardPage');
  const locale = useLocale();
  const translateCategory = useCategoryTranslation();
  const translateTableHeader = useTableHeaderTranslation();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      const inventoryData = await getInventoryItems();
      setItems(inventoryData);
      setLoading(false);
    }
    fetchData();
  }, []);

  const totalItems = items.length;
  const lowStockItems = items.filter(item => item.quantity < 50).length;

  return (
    <div className="container mx-auto py-8">
      <PageHeader title={t('title')} description={t('description')} />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('totalUniqueItems')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-1/2" /> : formatNumberArabic(totalItems, locale)}</div>
            <p className="text-xs text-muted-foreground">{t('totalUniqueItemsDesc')}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('totalQuantity')}</CardTitle>
            <ListTree className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? <Skeleton className="h-8 w-1/2" /> : formatNumberArabic(items.reduce((sum, item) => sum + item.quantity, 0), locale)}
            </div>
            <p className="text-xs text-muted-foreground">{t('totalQuantityDesc')}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('lowStockItems')}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{loading ? <Skeleton className="h-8 w-1/2" /> : formatNumberArabic(lowStockItems, locale)}</div>
            <p className="text-xs text-muted-foreground">{t('lowStockItemsDesc')}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('categories')}</CardTitle>
            <ListTree className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? <Skeleton className="h-8 w-1/2" /> : formatNumberArabic(new Set(items.map(item => item.category)).size, locale)}
            </div>
            <p className="text-xs text-muted-foreground">{t('categoriesDesc')}</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('currentInventoryTitle')}</CardTitle>
          <CardDescription>{t('currentInventoryDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-10 w-full" />)}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">{translateTableHeader('icon')}</TableHead>
                  <TableHead>{translateTableHeader('name')}</TableHead>
                  <TableHead>{translateTableHeader('category')}</TableHead>
                  <TableHead className={locale === 'ar' ? 'text-left' : 'text-right'}>{translateTableHeader('quantity')}</TableHead>
                  <TableHead>{translateTableHeader('unit')}</TableHead>
                  <TableHead>{translateTableHeader('lastUpdated')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{categoryIcons[item.category] || <Package className="h-5 w-5 text-gray-400" />}</TableCell>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>
                      <Badge variant={item.category === 'Copper Cable' ? 'default' : item.category === 'FTTH Cable' ? 'secondary' : 'outline'}>
                        {translateCategory(item.category)}
                      </Badge>
                    </TableCell>
                    <TableCell className={`${locale === 'ar' ? 'text-left' : 'text-right'} font-semibold ${item.quantity < 50 ? 'text-destructive' : ''}`}>
                      {formatNumberArabic(item.quantity, locale)}
                    </TableCell>
                    <TableCell>{item.unit}</TableCell>
                    <TableCell>{formatDateArabic(item.lastUpdated, locale)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
