"use client";

import * as React from "react";
import { useLocale } from 'next-intl';
import { cn } from "@/lib/utils";

const LocalizedTable = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => {
  const locale = useLocale();
  
  return (
    <div className="relative w-full overflow-auto">
      <table
        ref={ref}
        className={cn(
          "w-full caption-bottom text-sm",
          locale === 'ar' ? 'text-right' : 'text-left',
          className
        )}
        {...props}
      />
    </div>
  );
});
LocalizedTable.displayName = "LocalizedTable";

const LocalizedTableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
));
LocalizedTableHeader.displayName = "LocalizedTableHeader";

const LocalizedTableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
));
LocalizedTableBody.displayName = "LocalizedTableBody";

const LocalizedTableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      "border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",
      className
    )}
    {...props}
  />
));
LocalizedTableFooter.displayName = "LocalizedTableFooter";

const LocalizedTableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
      className
    )}
    {...props}
  />
));
LocalizedTableRow.displayName = "LocalizedTableRow";

const LocalizedTableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => {
  const locale = useLocale();
  
  return (
    <th
      ref={ref}
      className={cn(
        "h-12 px-4 font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
        locale === 'ar' ? 'text-right' : 'text-left',
        className
      )}
      {...props}
    />
  );
});
LocalizedTableHead.displayName = "LocalizedTableHead";

const LocalizedTableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => {
  const locale = useLocale();
  
  return (
    <td
      ref={ref}
      className={cn(
        "p-4 align-middle [&:has([role=checkbox])]:pr-0",
        locale === 'ar' ? 'text-right' : 'text-left',
        className
      )}
      {...props}
    />
  );
});
LocalizedTableCell.displayName = "LocalizedTableCell";

const LocalizedTableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-sm text-muted-foreground", className)}
    {...props}
  />
));
LocalizedTableCaption.displayName = "LocalizedTableCaption";

export {
  LocalizedTable,
  LocalizedTableHeader,
  LocalizedTableBody,
  LocalizedTableFooter,
  LocalizedTableHead,
  LocalizedTableRow,
  LocalizedTableCell,
  LocalizedTableCaption,
};
