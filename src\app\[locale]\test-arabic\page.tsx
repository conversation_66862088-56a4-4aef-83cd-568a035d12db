"use client";

import { useTranslations, useLocale } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useCategoryTranslation, useTransactionTypeTranslation, formatDateArabic, formatNumberArabic } from '@/lib/translations';
import { LocalizedNumber, LocalizedDate } from '@/components/ui/localized-text';
import { DirectionalFlex, DirectionalSpacing } from '@/components/ui/rtl-provider';
import PageHeader from '@/components/common/page-header';

export default function TestArabicPage() {
  const t = useTranslations('TestArabic');
  const locale = useLocale();
  const translateCategory = useCategoryTranslation();
  const translateTransactionType = useTransactionTypeTranslation();

  const testData = [
    {
      id: '1',
      name: 'كابل نحاس 2 زوج',
      category: 'Copper Cable',
      quantity: 1500,
      unit: 'متر',
      lastUpdated: new Date().toISOString(),
      type: 'Incoming'
    },
    {
      id: '2', 
      name: 'كابل ألياف بصرية 12 core',
      category: 'FTTH Cable',
      quantity: 750,
      unit: 'متر',
      lastUpdated: new Date().toISOString(),
      type: 'Outgoing'
    },
    {
      id: '3',
      name: 'موصل RJ45',
      category: 'Equipment',
      quantity: 200,
      unit: 'قطعة',
      lastUpdated: new Date().toISOString(),
      type: 'Consumption'
    }
  ];

  return (
    <div className="container mx-auto py-8 arabic-text">
      <PageHeader 
        title="اختبار التعريب" 
        description="صفحة اختبار لعرض جميع عناصر التعريب والـ RTL"
      />

      <DirectionalSpacing spacing="lg" className="mb-8">
        {/* بطاقات الإحصائيات */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الأصناف</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <LocalizedNumber value={1250} />
              </div>
              <p className="text-xs text-muted-foreground">
                +<LocalizedNumber value={180} /> من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الكمية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <LocalizedNumber value={45678} />
              </div>
              <p className="text-xs text-muted-foreground">
                آخر تحديث: <LocalizedDate value={new Date()} />
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مخزون منخفض</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                <LocalizedNumber value={23} />
              </div>
              <p className="text-xs text-muted-foreground">
                يحتاج إعادة تعبئة
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الفئات النشطة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <LocalizedNumber value={4} />
              </div>
              <p className="text-xs text-muted-foreground">
                من أصل <LocalizedNumber value={6} /> فئات
              </p>
            </CardContent>
          </Card>
        </div>

        {/* جدول اختبار التعريب */}
        <Card>
          <CardHeader>
            <CardTitle>جدول اختبار التعريب</CardTitle>
            <CardDescription>
              عرض البيانات مع التنسيق العربي الصحيح
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم الصنف</TableHead>
                  <TableHead>الفئة</TableHead>
                  <TableHead className={locale === 'ar' ? 'text-left' : 'text-right'}>الكمية</TableHead>
                  <TableHead>الوحدة</TableHead>
                  <TableHead>نوع المعاملة</TableHead>
                  <TableHead>آخر تحديث</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {testData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>
                      <Badge variant={item.category === 'Copper Cable' ? 'default' : 'secondary'}>
                        {translateCategory(item.category)}
                      </Badge>
                    </TableCell>
                    <TableCell className={`${locale === 'ar' ? 'text-left' : 'text-right'} font-semibold`}>
                      <LocalizedNumber value={item.quantity} />
                    </TableCell>
                    <TableCell>{item.unit}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {translateTransactionType(item.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <LocalizedDate value={item.lastUpdated} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* اختبار المكونات الاتجاهية */}
        <Card>
          <CardHeader>
            <CardTitle>اختبار المكونات الاتجاهية</CardTitle>
            <CardDescription>
              عرض المكونات مع دعم RTL
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DirectionalSpacing spacing="md" className="mb-4">
              <DirectionalFlex className="items-center gap-4">
                <Button variant="default">زر أساسي</Button>
                <Button variant="outline">زر ثانوي</Button>
                <Button variant="ghost">زر شفاف</Button>
              </DirectionalFlex>
            </DirectionalSpacing>

            <DirectionalSpacing spacing="sm" direction="vertical">
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">معلومات المخزون</h3>
                <DirectionalFlex className="justify-between items-center">
                  <span>إجمالي القيمة:</span>
                  <span className="font-bold">
                    <LocalizedNumber value={125000} /> ريال
                  </span>
                </DirectionalFlex>
              </div>

              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">آخر المعاملات</h3>
                <DirectionalFlex className="justify-between items-center">
                  <span>تاريخ آخر معاملة:</span>
                  <span className="text-muted-foreground">
                    <LocalizedDate value={new Date()} />
                  </span>
                </DirectionalFlex>
              </div>

              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">حالة النظام</h3>
                <DirectionalFlex className="justify-between items-center">
                  <span>عدد المستخدمين النشطين:</span>
                  <Badge variant="default">
                    <LocalizedNumber value={15} /> مستخدم
                  </Badge>
                </DirectionalFlex>
              </div>
            </DirectionalSpacing>
          </CardContent>
        </Card>

        {/* اختبار الألوان والتصميم */}
        <Card>
          <CardHeader>
            <CardTitle>اختبار الألوان والتصميم</CardTitle>
            <CardDescription>
              عرض الألوان والتصميم مع النصوص العربية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="p-4 bg-primary text-primary-foreground rounded-lg">
                <h4 className="font-semibold">اللون الأساسي</h4>
                <p className="text-sm opacity-90">نص تجريبي باللون الأساسي</p>
              </div>
              
              <div className="p-4 bg-secondary text-secondary-foreground rounded-lg">
                <h4 className="font-semibold">اللون الثانوي</h4>
                <p className="text-sm opacity-90">نص تجريبي باللون الثانوي</p>
              </div>
              
              <div className="p-4 bg-muted text-muted-foreground rounded-lg">
                <h4 className="font-semibold">اللون المكتوم</h4>
                <p className="text-sm">نص تجريبي باللون المكتوم</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </DirectionalSpacing>
    </div>
  );
}
