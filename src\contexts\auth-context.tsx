
"use client";

import type { User, UserRole } from '@/types'; 
import { usePathname, useRouter } from 'next/navigation';
import type { ReactNode } from 'react';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { useLocale } from 'next-intl';

interface AuthContextType {
  user: User | null;
  login: (username: string, pass: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users for demonstration
const MOCK_USERS: User[] = [
  { id: 'admin-001', username: 'admin', email: '<EMAIL>', name: 'Administrator', role: 'admin' },
  { id: 'supervisor-001', username: 'supervisor', email: '<EMAIL>', name: 'Supervisor Alpha', role: 'supervisor' },
  { id: 'user-001', username: 'user1', email: '<EMAIL>', name: 'User One', role: 'user' },
];

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      // In a real app, you'd check a persistent session (e.g., from localStorage or Supabase)
      // For this mock, we'll just ensure loading state is handled.
      // If you wanted to persist mock login:
      // const storedUserJson = localStorage.getItem('mockUser');
      // if (storedUserJson) {
      //   try {
      //     const storedUser = JSON.parse(storedUserJson) as User;
      //     // Validate if this user still exists in our mock list or has valid role
      //     const validMockUser = MOCK_USERS.find(u => u.id === storedUser.id && u.username === storedUser.username);
      //     if(validMockUser) setUser(validMockUser);
      //   } catch (e) {
      //     localStorage.removeItem('mockUser');
      //   }
      // }
      setLoading(false);
    };
    checkAuth();
  }, []);

  const login = useCallback(async (usernameInput: string, pass: string) => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
    
    let foundUser: User | undefined = undefined;

    if (usernameInput.toLowerCase() === 'admin' && pass === 'admin') {
      foundUser = MOCK_USERS.find(u => u.role === 'admin');
    } else if (usernameInput.toLowerCase() === 'supervisor' && pass === 'supervisor') {
      foundUser = MOCK_USERS.find(u => u.role === 'supervisor');
    } else if (usernameInput.toLowerCase() === 'user1' && pass === 'user1') { // Example regular user
      foundUser = MOCK_USERS.find(u => u.username === 'user1');
    }

    if (foundUser) {
      setUser(foundUser);
      // localStorage.setItem('mockUser', JSON.stringify(foundUser)); 
      router.push(`/${locale}/`); 
    } else {
      alert('Invalid credentials. Try: admin/admin, supervisor/supervisor, or user1/user1');
    }
    setLoading(false);
  }, [router, locale]);

  const logout = useCallback(() => {
    setUser(null);
    // localStorage.removeItem('mockUser');
    router.push(`/${locale}/login`);
  }, [router, locale]);

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{ user, login, logout, loading, isAuthenticated }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
