
"use client";

import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Menu, UserCircle, HardDrive, Boxes, LogOut, Users } from "lucide-react";
import Link from "next/link";
import { useSidebar } from "@/components/ui/sidebar"; 
import { usePathname, useRouter } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type React from "react";
import { useAuth } from "@/contexts/auth-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const LayoutDashboardIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
);
const ArrowRightLeftIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M17 2.05V2h-3"></path><path d="M14 2L22 10"></path><path d="M7 22.05V22h3"></path><path d="M10 22L2 14"></path><path d="M22 10H2"></path><path d="M2 14H12"></path></svg>
);
const FileTextIcon = (props: React.SVGProps<SVGSVGElement>) => (
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><line x1="10" y1="9" x2="8" y2="9"></line></svg>
);

interface MobileNavItem {
  hrefKey: string;
  labelKey: keyof ReturnType<typeof useTranslations<'Navigation'>>;
  icon: React.ElementType;
  adminOnly?: boolean;
}

const staticMobileNavItems: MobileNavItem[] = [
  { hrefKey: "/", labelKey: "dashboard", icon: LayoutDashboardIcon },
  { hrefKey: "/items", labelKey: "manageItems", icon: Boxes },
  { hrefKey: "/transactions", labelKey: "transactions", icon: ArrowRightLeftIcon },
  { hrefKey: "/reports", labelKey: "reports", icon: FileTextIcon },
  { hrefKey: "/users", labelKey: "userManagement", icon: Users, adminOnly: true },
];


export function MainHeader() {
  const { toggleSidebar, isMobile } = useSidebar();
  const tNav = useTranslations('Navigation');
  const tHeader = useTranslations('MainHeader');
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale();
  const { user, logout, isAuthenticated } = useAuth();

  const mobileNavItems = staticMobileNavItems;

  const changeLocale = (newLocale: string) => {
    if (currentLocale === newLocale) return;
    let newPath = pathname;
    if (pathname === `/${currentLocale}` || pathname === `/${currentLocale}/`) {
      newPath = `/${newLocale}/`;
    } else {
      newPath = pathname.replace(`/${currentLocale}/`, `/${newLocale}/`);
      if (!newPath.startsWith(`/${newLocale}`)) { // Handle cases like /login -> /ar/login
          newPath = `/${newLocale}${pathname.startsWith('/') ? '' : '/'}${pathname.substring(pathname.startsWith(`/${currentLocale}`) ? `/${currentLocale}`.length : 0)}`;
      }
    }
    router.replace(newPath);
  };
  
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background/80 backdrop-blur-sm px-4 md:px-6">
      {isMobile ? (
         <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="shrink-0 md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">{tHeader('toggleNavigationMenu')}</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="flex flex-col p-0">
               <div className="p-4 border-b">
                 <Link href={`/${currentLocale}/`} className="flex items-center gap-2">
                   <HardDrive className="h-7 w-7 text-primary" />
                   <h1 className="text-lg font-headline font-bold text-primary">{tNav('copperFlow')}</h1>
                 </Link>
               </div>
               <nav className="flex-1 overflow-y-auto p-4 space-y-1">
                 {mobileNavItems.map(item => {
                   if (item.adminOnly && (!user || user.role !== 'admin')) {
                     return null; 
                   }
                   return (
                    <Link 
                      key={item.hrefKey}
                      href={`/${currentLocale}${item.hrefKey === "/" ? "" : item.hrefKey}`} 
                      className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
                      onClick={() => {
                        const sheetCloseButton = document.querySelector('[data-radix-dialog-default-open="false"] > button[aria-label="Close"]');
                        if (sheetCloseButton instanceof HTMLElement) {
                          sheetCloseButton.click();
                        }
                      }}
                    >
                      <item.icon className="h-4 w-4" />
                      {tNav(item.labelKey)}
                   </Link>
                   );
                 })}
               </nav>
            </SheetContent>
          </Sheet>
      ) : (
        <Button variant="outline" size="icon" className="shrink-0" onClick={toggleSidebar}>
          <Menu className="h-5 w-5" />
          <span className="sr-only">{tHeader('toggleSidebar')}</span>
        </Button>
      )}

      <div className="flex w-full items-center justify-end gap-4 md:ml-auto md:gap-2 lg:gap-4">
        <Select value={currentLocale} onValueChange={changeLocale}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder={tHeader('language')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="en">English</SelectItem>
            <SelectItem value="ar">العربية</SelectItem>
          </SelectContent>
        </Select>

        {isAuthenticated && user && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <UserCircle className="h-6 w-6" />
                <span className="sr-only">{tHeader('userMenu')}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{user.name || user.username || user.email}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>
                <LogOut className="mr-2 h-4 w-4 rtl:mr-0 rtl:ml-2" />
                {tHeader('logout')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
}
