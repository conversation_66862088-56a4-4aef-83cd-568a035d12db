
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { useState } from 'react';
import { useTranslations } from 'next-intl';

// Note: Item categories might also need translation if they are displayed to the user
// For now, keeping them as is, assuming they are internal identifiers or will be handled.
const itemCategories = ['Copper Cable', 'FTTH Cable', 'Equipment', 'Other'] as const;

const formSchema = z.object({
  name: z.string().min(1, "Item name is required."),
  category: z.enum(itemCategories, { required_error: "Category is required." }),
  quantity: z.coerce.number().min(0, "Quantity cannot be negative."),
  unit: z.string().min(1, "Unit is required (e.g., meters, units)."),
});

export type ItemFormValues = z.infer<typeof formSchema>;

interface ItemFormProps {
  onSubmit: (data: ItemFormValues) => Promise<void>;
  onClose: () => void;
  defaultValues?: Partial<ItemFormValues>;
}

export function ItemForm({ onSubmit, onClose, defaultValues }: ItemFormProps) {
  const t = useTranslations('ItemForm');
  const form = useForm<ItemFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues || {
      name: "",
      category: undefined,
      quantity: 0,
      unit: "",
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  async function handleSubmit(data: ItemFormValues) {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Item form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('itemNameLabel')}</FormLabel>
              <FormControl>
                <Input placeholder={t('itemNamePlaceholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('categoryLabel')}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t('selectCategoryPlaceholder')} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {itemCategories.map((cat) => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem> // Category names might need translation
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
           <FormField
            control={form.control}
            name="unit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('unitLabel')}</FormLabel>
                <FormControl>
                  <Input placeholder={t('unitPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('quantityLabel')}</FormLabel>
                <FormControl>
                  <Input type="number" placeholder={t('quantityPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>{t('cancelButton')}</Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin rtl:mr-0 rtl:ml-2" />}
              {isSubmitting ? t('savingButton') : (defaultValues ? t('saveChangesButton') : t('addItemButton'))}
            </Button>
        </div>
      </form>
    </Form>
  );
}
