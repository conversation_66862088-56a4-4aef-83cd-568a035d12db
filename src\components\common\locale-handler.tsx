// src/components/common/locale-handler.tsx
"use client";

import { useEffect } from 'react';
// import { usePathname } from 'next/navigation'; // Not strictly needed if locale is passed as prop
// import { useLocale } from 'next-intl'; // Not strictly needed if locale is passed as prop

interface LocaleHandlerProps {
  locale: string;
  children: React.ReactNode;
}

export default function LocaleHandler({ locale, children }: LocaleHandlerProps) {
  useEffect(() => {
    // This code runs only on the client-side after hydration
    if (typeof document !== 'undefined') {
      document.documentElement.lang = locale;
      document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    }
  }, [locale]);

  return <>{children}</>;
}
