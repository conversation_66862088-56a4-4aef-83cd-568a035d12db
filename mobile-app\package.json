{"name": "copper-flow-mobile", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/bottom-tabs": "^6.5.11", "@supabase/supabase-js": "^2.38.4", "expo": "~50.0.0", "expo-auth-session": "~5.4.0", "expo-barcode-scanner": "~12.9.0", "expo-camera": "~14.1.0", "expo-constants": "~15.4.0", "expo-crypto": "~12.8.0", "expo-dev-client": "~3.3.0", "expo-font": "~11.10.0", "expo-linking": "~6.2.0", "expo-location": "~16.5.0", "expo-notifications": "~0.27.0", "expo-router": "~3.4.0", "expo-secure-store": "~12.8.0", "expo-splash-screen": "~0.26.0", "expo-status-bar": "~1.11.0", "expo-web-browser": "~12.8.0", "react": "18.2.0", "react-native": "0.73.0", "react-native-elements": "^3.4.3", "react-native-paper": "^5.11.6", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-vector-icons": "^10.0.3", "react-hook-form": "^7.48.2", "date-fns": "^2.30.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@types/react": "~18.2.45", "@types/react-native": "^0.72.8", "typescript": "^5.3.0"}, "expo": {"name": "Copper Flow Mobile", "slug": "copper-flow-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#008080"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.copperflow.mobile"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#008080"}, "package": "com.copperflow.mobile", "permissions": ["CAMERA", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "VIBRATE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-barcode-scanner", {"cameraPermission": "Allow Copper Flow to access camera for barcode scanning."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow Copper Flow to use your location for tracking inventory movements."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#008080"}]]}}