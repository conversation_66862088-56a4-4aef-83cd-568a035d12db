
"use client";

import { useEffect, useState, useCallback } from 'react';
import type { InventoryItem, Transaction, ReportFilters as ReportFiltersType } from '@/types';
import { getInventoryItems, getTransactions } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ReportFilters } from './components/report-filters';
import { ReportTable } from './components/report-table';
import { DetailedTransactionReportTable } from './components/detailed-transaction-report-table';
import PageHeader from '@/components/common/page-header';
import { Loader2 } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';

interface ReportDataItem {
  itemId: string;
  itemName: string;
  category: InventoryItem['category'];
  unit: string;
  incoming: number;
  outgoing: number;
  consumption: number;
  currentQuantity: number;
}

// Add itemCategory and itemUnit to Transaction for the detailed report
interface EnrichedTransaction extends Transaction {
  itemCategory?: InventoryItem['category'];
  itemUnit?: string;
}

export default function ReportsPage() {
  const t = useTranslations('ReportsPage');
  const [allInventoryItems, setAllInventoryItems] = useState<InventoryItem[]>([]);
  const [allTransactions, setAllTransactions] = useState<Transaction[]>([]);
  
  const [summaryReportData, setSummaryReportData] = useState<ReportDataItem[]>([]);
  const [detailedTransactionsData, setDetailedTransactionsData] = useState<EnrichedTransaction[]>([]);
  
  const [loading, setLoading] = useState(true);
  const [currentFilters, setCurrentFilters] = useState<ReportFiltersType>({});
  const { toast } = useToast();

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [itemsData, transactionsData] = await Promise.all([
        getInventoryItems(),
        getTransactions(),
      ]);
      setAllInventoryItems(itemsData);
      // Sort transactions by date descending by default
      setAllTransactions(transactionsData.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
    } catch (error) {
       toast({
        title: t('toast.errorFetchingData.title'),
        description: t('toast.errorFetchingData.description'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast, t]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const generateReports = useCallback((filters: ReportFiltersType) => {
    setLoading(true);
    let filteredTransactions = [...allTransactions];

    if (filters.dateFrom) {
      filteredTransactions = filteredTransactions.filter(t => new Date(t.date) >= new Date(filters.dateFrom!));
    }
    if (filters.dateTo) {
      const dateToInclusive = new Date(filters.dateTo!);
      dateToInclusive.setDate(dateToInclusive.getDate() + 1);
      filteredTransactions = filteredTransactions.filter(t => new Date(t.date) < dateToInclusive);
    }
    if (filters.location) {
      filteredTransactions = filteredTransactions.filter(t => t.location?.toLowerCase().includes(filters.location!.toLowerCase()));
    }
    if (filters.employee) {
      filteredTransactions = filteredTransactions.filter(t => t.personnelInvolved?.toLowerCase().includes(filters.employee!.toLowerCase()));
    }

    // Generate Summary Report Data
    const summaryData = allInventoryItems.map(item => {
      const itemTransactions = filteredTransactions.filter(t => t.itemId === item.id);
      return {
        itemId: item.id,
        itemName: item.name,
        category: item.category,
        unit: item.unit,
        incoming: itemTransactions.filter(t => t.type === 'Incoming').reduce((sum, t) => sum + t.quantity, 0),
        outgoing: itemTransactions.filter(t => t.type === 'Outgoing').reduce((sum, t) => sum + t.quantity, 0),
        consumption: itemTransactions.filter(t => t.type === 'Consumption').reduce((sum, t) => sum + t.quantity, 0),
        currentQuantity: item.quantity,
      };
    });
    setSummaryReportData(summaryData);

    // Generate Detailed Transaction Report Data
    const enrichedDetailedTransactions = filteredTransactions.map(txn => {
      const item = allInventoryItems.find(i => i.id === txn.itemId);
      return {
        ...txn,
        itemCategory: item?.category,
        itemUnit: item?.unit,
      };
    });
    setDetailedTransactionsData(enrichedDetailedTransactions);
    
    setCurrentFilters(filters);
    setLoading(false);
  }, [allInventoryItems, allTransactions, t]);
  
  useEffect(() => {
    if (allInventoryItems.length > 0 || allTransactions.length > 0) { // Generate even if one is empty to show empty states
      generateReports({}); 
    }
  }, [allInventoryItems, allTransactions, generateReports]);


  const handleApplyFilters = (filters: ReportFiltersType) => {
    generateReports(filters);
  };

  const handleClearFilters = () => {
    generateReports({});
  };

  return (
    <div className="container mx-auto py-8">
      <PageHeader title={t('title')} description={t('description')} />

      <ReportFilters 
        onApplyFilters={handleApplyFilters} 
        onClearFilters={handleClearFilters}
        initialFilters={currentFilters}
      />

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>{t('summaryReportCard.title')}</CardTitle>
          <CardDescription>{t('summaryReportCard.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading && summaryReportData.length === 0 ? ( // Show loader only if initial data isn't there yet
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : (
            <ReportTable data={summaryReportData} />
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('detailedTransactionReportCard.title')}</CardTitle>
          <CardDescription>{t('detailedTransactionReportCard.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading && detailedTransactionsData.length === 0 ? ( // Show loader only if initial data isn't there yet
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : (
            <DetailedTransactionReportTable transactions={detailedTransactionsData} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
