"use client";

import { useEffect, useState, useCallback } from 'react';
import type { InventoryItem, Transaction, ReportFilters as ReportFiltersType } from '@/types';
import { getInventoryItems, getTransactions } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ReportFilters } from './components/report-filters';
import { ReportTable } from './components/report-table';
import PageHeader from '@/components/common/page-header';
import { Loader2 } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

interface ReportDataItem {
  itemId: string;
  itemName: string;
  category: InventoryItem['category'];
  unit: string;
  incoming: number;
  outgoing: number;
  consumption: number;
  currentQuantity: number;
}

export default function ReportsPage() {
  const [allInventoryItems, setAllInventoryItems] = useState<InventoryItem[]>([]);
  const [allTransactions, setAllTransactions] = useState<Transaction[]>([]);
  const [filteredReportData, setFilteredReportData] = useState<ReportDataItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentFilters, setCurrentFilters] = useState<ReportFiltersType>({});
  const { toast } = useToast();

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [itemsData, transactionsData] = await Promise.all([
        getInventoryItems(),
        getTransactions(),
      ]);
      setAllInventoryItems(itemsData);
      setAllTransactions(transactionsData);
    } catch (error) {
       toast({
        title: "Error fetching data",
        description: "Could not load initial data for reports.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const generateReportData = useCallback((filters: ReportFiltersType) => {
    setLoading(true);
    let filteredTransactions = [...allTransactions];

    if (filters.dateFrom) {
      filteredTransactions = filteredTransactions.filter(t => new Date(t.date) >= new Date(filters.dateFrom!));
    }
    if (filters.dateTo) {
      // Add 1 day to dateTo to make it inclusive of the selected day
      const dateToInclusive = new Date(filters.dateTo!);
      dateToInclusive.setDate(dateToInclusive.getDate() + 1);
      filteredTransactions = filteredTransactions.filter(t => new Date(t.date) < dateToInclusive);
    }
    if (filters.location) {
      filteredTransactions = filteredTransactions.filter(t => t.location?.toLowerCase().includes(filters.location!.toLowerCase()));
    }
    if (filters.employee) {
      filteredTransactions = filteredTransactions.filter(t => t.personnelInvolved?.toLowerCase().includes(filters.employee!.toLowerCase()));
    }

    const reportData = allInventoryItems.map(item => {
      const itemTransactions = filteredTransactions.filter(t => t.itemId === item.id);
      return {
        itemId: item.id,
        itemName: item.name,
        category: item.category,
        unit: item.unit,
        incoming: itemTransactions.filter(t => t.type === 'Incoming').reduce((sum, t) => sum + t.quantity, 0),
        outgoing: itemTransactions.filter(t => t.type === 'Outgoing').reduce((sum, t) => sum + t.quantity, 0),
        consumption: itemTransactions.filter(t => t.type === 'Consumption').reduce((sum, t) => sum + t.quantity, 0),
        currentQuantity: item.quantity, // This is the overall current quantity, not specific to date range
      };
    });
    
    setFilteredReportData(reportData);
    setCurrentFilters(filters);
    setLoading(false);
  }, [allInventoryItems, allTransactions]);
  
  // Generate initial report with all data once loaded
  useEffect(() => {
    if (allInventoryItems.length > 0 && allTransactions.length > 0) {
      generateReportData({}); // Generate with no filters initially
    }
  }, [allInventoryItems, allTransactions, generateReportData]);


  const handleApplyFilters = (filters: ReportFiltersType) => {
    generateReportData(filters);
  };

  const handleClearFilters = () => {
    generateReportData({});
  };


  return (
    <div className="container mx-auto py-8">
      <PageHeader title="Inventory Reports" description="Analyze inventory movements based on filters." />

      <ReportFilters 
        onApplyFilters={handleApplyFilters} 
        onClearFilters={handleClearFilters}
        initialFilters={currentFilters}
      />

      <Card>
        <CardHeader>
          <CardTitle>Report Summary</CardTitle>
          <CardDescription>
            Inventory summary based on the applied filters.
            Offline functionality (making changes offline and syncing later) is a complex feature requiring service workers and local database (e.g., IndexedDB) which is beyond this initial setup.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : (
            <ReportTable data={filteredReportData} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
