
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { LayoutDashboard, ArrowRightLeft, FileText, HardDrive, Boxes, Users, Settings, BarChart, Package, Truck, MapPin, Calendar } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarSeparator,
} from "@/components/ui/sidebar";
import { useLocale, useTranslations } from 'next-intl';
import React, { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";

interface NavItem {
  hrefKey: string;
  labelKey: keyof ReturnType<typeof useTranslations<'Navigation'>>;
  icon: React.ElementType;
  adminOnly?: boolean;
}

// Main navigation items
const mainNavItems: NavItem[] = [
  { hrefKey: "/", labelKey: "dashboard", icon: LayoutDashboard },
  { hrefKey: "/items", labelKey: "manageItems", icon: Boxes },
  { hrefKey: "/transactions", labelKey: "transactions", icon: ArrowRightLeft },
  { hrefKey: "/reports", labelKey: "reports", icon: FileText },
];

// Analytics and insights items
const analyticsNavItems: NavItem[] = [
  { hrefKey: "/analytics", labelKey: "analytics", icon: BarChart },
  { hrefKey: "/inventory", labelKey: "inventory", icon: Package },
];

// Management items
const managementNavItems: NavItem[] = [
  { hrefKey: "/suppliers", labelKey: "suppliers", icon: Truck },
  { hrefKey: "/locations", labelKey: "locations", icon: MapPin },
  { hrefKey: "/schedule", labelKey: "schedule", icon: Calendar },
];

// System items
const systemNavItems: NavItem[] = [
  { hrefKey: "/settings", labelKey: "settings", icon: Settings },
  { hrefKey: "/users", labelKey: "userManagement", icon: Users, adminOnly: true },
];

// All items combined for active state detection
const allNavItems = [...mainNavItems, ...analyticsNavItems, ...managementNavItems, ...systemNavItems];

export function MainSidebar() {
  const currentPathname = usePathname();
  const locale = useLocale();
  const t = useTranslations('Navigation');
  const { user, loading: authLoading } = useAuth();

  const [currentYear, setCurrentYear] = useState(0);
  const [activeHrefKey, setActiveHrefKey] = useState<string | null>(null);

  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);

  useEffect(() => {
    // Derive active path without locale prefix
    const pathWithoutLocale = currentPathname.startsWith(`/${locale}`)
      ? currentPathname.substring(`/${locale}`.length) || "/"
      : currentPathname;

    // Determine the most specific active item
    let currentActive: string | null = null;
    let longestMatchLength = 0;

    for (const item of allNavItems) {
      const itemPath = item.hrefKey === "/" ? "/" : item.hrefKey;
      if (pathWithoutLocale === itemPath || (itemPath !== "/" && pathWithoutLocale.startsWith(itemPath))) {
        if (itemPath.length > longestMatchLength) {
          currentActive = item.hrefKey;
          longestMatchLength = itemPath.length;
        }
      }
    }
    setActiveHrefKey(currentActive);
  }, [currentPathname, locale]);


  return (
    <Sidebar collapsible="icon" className={locale === 'ar' ? 'sidebar-right' : 'sidebar-left'}>
      <SidebarHeader className="p-4">
        <Link href={`/${locale}/`} className={`flex items-center gap-2 ${locale === 'ar' ? 'flex-row-reverse' : ''}`}>
          <HardDrive className="h-8 w-8 text-primary" />
          <h1 className="text-xl font-headline font-bold text-primary group-data-[collapsible=icon]:hidden arabic-text">
            {t('copperFlow')}
          </h1>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel className="arabic-text">الرئيسية</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainNavItems.map((item) => {
                const linkHref = `/${locale}${item.hrefKey === "/" ? "" : item.hrefKey}`;
                const isActive = activeHrefKey === item.hrefKey;

                return (
                  <SidebarMenuItem key={item.hrefKey}>
                    <Link href={linkHref} legacyBehavior passHref>
                      <SidebarMenuButton
                        variant="default"
                        className={cn(
                          "arabic-text",
                          locale === 'ar' ? 'flex-row-reverse' : ''
                        )}
                        asChild={false}
                        isActive={isActive}
                        tooltip={{ children: t(item.labelKey) }}
                      >
                        <item.icon className="h-5 w-5" />
                        <span className="group-data-[collapsible=icon]:hidden">{t(item.labelKey)}</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Analytics */}
        <SidebarGroup>
          <SidebarGroupLabel className="arabic-text">التحليلات</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {analyticsNavItems.map((item) => {
                const linkHref = `/${locale}${item.hrefKey === "/" ? "" : item.hrefKey}`;
                const isActive = activeHrefKey === item.hrefKey;

                return (
                  <SidebarMenuItem key={item.hrefKey}>
                    <Link href={linkHref} legacyBehavior passHref>
                      <SidebarMenuButton
                        variant="default"
                        className={cn(
                          "arabic-text",
                          locale === 'ar' ? 'flex-row-reverse' : ''
                        )}
                        asChild={false}
                        isActive={isActive}
                        tooltip={{ children: t(item.labelKey) }}
                      >
                        <item.icon className="h-5 w-5" />
                        <span className="group-data-[collapsible=icon]:hidden">{t(item.labelKey)}</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Management */}
        <SidebarGroup>
          <SidebarGroupLabel className="arabic-text">الإدارة</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {managementNavItems.map((item) => {
                const linkHref = `/${locale}${item.hrefKey === "/" ? "" : item.hrefKey}`;
                const isActive = activeHrefKey === item.hrefKey;

                return (
                  <SidebarMenuItem key={item.hrefKey}>
                    <Link href={linkHref} legacyBehavior passHref>
                      <SidebarMenuButton
                        variant="default"
                        className={cn(
                          "arabic-text",
                          locale === 'ar' ? 'flex-row-reverse' : ''
                        )}
                        asChild={false}
                        isActive={isActive}
                        tooltip={{ children: t(item.labelKey) }}
                      >
                        <item.icon className="h-5 w-5" />
                        <span className="group-data-[collapsible=icon]:hidden">{t(item.labelKey)}</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* System */}
        <SidebarGroup>
          <SidebarGroupLabel className="arabic-text">النظام</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {systemNavItems.map((item) => {
                if (item.adminOnly && (!user || user.role !== 'admin')) {
                  return null; // Don't render admin-only links for non-admins
                }

                const linkHref = `/${locale}${item.hrefKey === "/" ? "" : item.hrefKey}`;
                const isActive = activeHrefKey === item.hrefKey;

                return (
                  <SidebarMenuItem key={item.hrefKey}>
                    <Link href={linkHref} legacyBehavior passHref>
                      <SidebarMenuButton
                        variant="default"
                        className={cn(
                          "arabic-text",
                          locale === 'ar' ? 'flex-row-reverse' : ''
                        )}
                        asChild={false}
                        isActive={isActive}
                        tooltip={{ children: t(item.labelKey) }}
                      >
                        <item.icon className="h-5 w-5" />
                        <span className="group-data-[collapsible=icon]:hidden">{t(item.labelKey)}</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="p-2 group-data-[collapsible=icon]:hidden">
        {currentYear > 0 && <p className="text-xs text-sidebar-foreground/70 text-center">© {currentYear} {t('copperFlow')}</p>}
      </SidebarFooter>
    </Sidebar>
  );
}
