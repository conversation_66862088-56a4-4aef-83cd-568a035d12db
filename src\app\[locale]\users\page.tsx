
"use client";

import { useEffect, useState, useCallback } from 'react';
import type { User, UserRole } from '@/types';
import { getAppUsers, updateAppUserRole } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import PageHeader from '@/components/common/page-header';
import { Loader2, ShieldCheck, ShieldAlert, UserCog, Users } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';
import { useAuth } from '@/contexts/auth-context'; // To ensure only admins see this
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { cn } from "@/lib/utils"; // Added missing import

const roleIcons: Record<UserRole, JSX.Element> = {
  admin: <ShieldCheck className="h-5 w-5 text-destructive" />,
  supervisor: <ShieldAlert className="h-5 w-5 text-amber-600" />,
  user: <UserCog className="h-5 w-5 text-blue-600" />,
};

const userRoles: UserRole[] = ['admin', 'supervisor', 'user'];

export default function UserManagementPage() {
  const t = useTranslations('UserManagementPage');
  const tCommonRoles = useTranslations('UserRoles');
  const { toast } = useToast();
  const { user: currentUser, loading: authLoading } = useAuth();
  const router = useRouter();
  const locale = useLocale();

  const [users, setUsers] = useState<User[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [updatingRoleId, setUpdatingRoleId] = useState<string | null>(null);


  useEffect(() => {
    if (!authLoading && (!currentUser || currentUser.role !== 'admin')) {
      router.replace(`/${locale}/`); // Redirect if not admin or not loaded
    }
  }, [currentUser, authLoading, router, locale]);

  const fetchData = useCallback(async () => {
    if (currentUser?.role !== 'admin') return; // Double check before fetching
    setLoadingData(true);
    try {
      const usersData = await getAppUsers();
      setUsers(usersData.sort((a, b) => (a.name || a.username || '').localeCompare(b.name || b.username || '')));
    } catch (error) {
      toast({
        title: t('toast.errorFetching.title'),
        description: t('toast.errorFetching.description'),
        variant: "destructive",
      });
    } finally {
      setLoadingData(false);
    }
  }, [toast, t, currentUser]);

  useEffect(() => {
    if (!authLoading && currentUser?.role === 'admin') {
      fetchData();
    }
  }, [fetchData, authLoading, currentUser]);

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    setUpdatingRoleId(userId);
    try {
      const updatedUser = await updateAppUserRole(userId, newRole);
      if (updatedUser) {
        setUsers(prevUsers => 
          prevUsers.map(u => u.id === userId ? { ...u, role: newRole } : u)
        );
        toast({
          title: t('toast.roleUpdated.title'),
          description: t('toast.roleUpdated.description', { userName: updatedUser.name || updatedUser.username, role: tCommonRoles(newRole) }),
        });
      }
    } catch (error) {
      toast({
        title: t('toast.errorUpdatingRole.title'),
        description: (error instanceof Error ? error.message : t('toast.errorUpdatingRole.description')),
        variant: "destructive",
      });
    } finally {
      setUpdatingRoleId(null);
    }
  };
  
  if (authLoading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
      </div>
    );
  }

  if (!currentUser || currentUser.role !== 'admin') {
    return (
      <div className="container mx-auto py-8 text-center">
        <p className="text-destructive text-lg">{t('unauthorizedAccess')}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <PageHeader title={t('title')} description={t('description')} />

      <Card>
        <CardHeader>
          <CardTitle>{t('userListTitle')}</CardTitle>
          <CardDescription>{t('userListDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">{t('noUsersFound')}</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">{t('tableIcon')}</TableHead>
                  <TableHead>{t('tableName')}</TableHead>
                  <TableHead>{t('tableEmail')}</TableHead>
                  <TableHead>{t('tableRole')}</TableHead>
                  <TableHead className="w-[200px] text-right rtl:text-left">{t('tableActions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{roleIcons[user.role] || <Users className="h-5 w-5 text-gray-400" />}</TableCell>
                    <TableCell className="font-medium">{user.name || user.username}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                       <span className={cn(
                        "font-semibold",
                        user.role === 'admin' && "text-destructive",
                        user.role === 'supervisor' && "text-amber-600",
                        user.role === 'user' && "text-blue-600"
                       )}>
                        {tCommonRoles(user.role)}
                       </span>
                    </TableCell>
                    <TableCell className="text-right rtl:text-left">
                      {updatingRoleId === user.id ? (
                        <Loader2 className="h-5 w-5 animate-spin text-primary inline-block" />
                      ) : (
                        <Select
                          value={user.role}
                          onValueChange={(newRole: string) => handleRoleChange(user.id, newRole as UserRole)}
                          disabled={user.id === currentUser.id} // Prevent admin from changing their own role via this UI
                        >
                          <SelectTrigger className="w-[160px]">
                            <SelectValue placeholder={t('selectRolePlaceholder')} />
                          </SelectTrigger>
                          <SelectContent>
                            {userRoles.map((role) => (
                              <SelectItem key={role} value={role} disabled={role === 'admin' && currentUser.role !== 'admin' /* Future: Super admin can create other admins*/}>
                                {tCommonRoles(role)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
