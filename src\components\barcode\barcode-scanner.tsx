"use client";

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Scan, Camera, X, Check, Package } from 'lucide-react';
import type { InventoryItem } from '@/types';
import { getInventoryItems } from '@/lib/data';
import { useToast } from '@/hooks/use-toast';

interface BarcodeScannerProps {
  onItemScanned?: (item: InventoryItem) => void;
  onClose?: () => void;
}

export function BarcodeScanner({ onItemScanned, onClose }: BarcodeScannerProps) {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState('');
  const [manualCode, setManualCode] = useState('');
  const [foundItem, setFoundItem] = useState<InventoryItem | null>(null);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    async function loadInventory() {
      const items = await getInventoryItems();
      setInventoryItems(items);
    }
    loadInventory();
  }, []);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 1280 },
          height: { ideal: 720 }
        } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsScanning(true);
      }
    } catch (error) {
      toast({
        title: "خطأ في الكاميرا",
        description: "لا يمكن الوصول إلى الكاميرا. تأكد من منح الإذن للتطبيق.",
        variant: "destructive",
      });
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  const searchItemByBarcode = (barcode: string): InventoryItem | null => {
    // In a real implementation, items would have barcode fields
    // For demo purposes, we'll simulate barcode matching
    const item = inventoryItems.find(item => 
      item.id === barcode || 
      item.name.toLowerCase().includes(barcode.toLowerCase()) ||
      // Simulate some items having barcodes
      (barcode === '123456789' && item.name.includes('Copper')) ||
      (barcode === '987654321' && item.name.includes('FTTH'))
    );
    return item || null;
  };

  const handleBarcodeDetected = (barcode: string) => {
    setScannedCode(barcode);
    const item = searchItemByBarcode(barcode);
    
    if (item) {
      setFoundItem(item);
      toast({
        title: "تم العثور على المادة",
        description: `تم العثور على: ${item.name}`,
      });
    } else {
      toast({
        title: "لم يتم العثور على المادة",
        description: `الباركود ${barcode} غير موجود في النظام`,
        variant: "destructive",
      });
    }
    
    stopCamera();
  };

  const handleManualEntry = () => {
    if (manualCode.trim()) {
      handleBarcodeDetected(manualCode.trim());
    }
  };

  const handleSelectItem = () => {
    if (foundItem && onItemScanned) {
      onItemScanned(foundItem);
      toast({
        title: "تم تحديد المادة",
        description: `تم تحديد: ${foundItem.name}`,
      });
    }
    onClose?.();
  };

  const handleReset = () => {
    setScannedCode('');
    setManualCode('');
    setFoundItem(null);
    stopCamera();
  };

  // Simulate barcode detection (in real app, use a barcode library like ZXing)
  useEffect(() => {
    if (isScanning) {
      // Simulate barcode detection after 3 seconds for demo
      const timer = setTimeout(() => {
        // Simulate detecting a barcode
        const demoBarcodes = ['123456789', '987654321', 'item-1', 'item-2'];
        const randomBarcode = demoBarcodes[Math.floor(Math.random() * demoBarcodes.length)];
        handleBarcodeDetected(randomBarcode);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isScanning]);

  return (
    <div className="space-y-4">
      {/* Camera Scanner */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scan className="h-5 w-5" />
            مسح الباركود
          </CardTitle>
          <CardDescription>
            استخدم الكاميرا لمسح الباركود أو أدخل الرمز يدوياً
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!isScanning && !foundItem && (
            <div className="space-y-4">
              <Button onClick={startCamera} className="w-full">
                <Camera className="mr-2 h-4 w-4" />
                بدء المسح بالكاميرا
              </Button>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">أو</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="manual-barcode">إدخال الباركود يدوياً</Label>
                <div className="flex gap-2">
                  <Input
                    id="manual-barcode"
                    placeholder="أدخل رمز الباركود"
                    value={manualCode}
                    onChange={(e) => setManualCode(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleManualEntry()}
                  />
                  <Button onClick={handleManualEntry} disabled={!manualCode.trim()}>
                    بحث
                  </Button>
                </div>
              </div>
            </div>
          )}

          {isScanning && (
            <div className="space-y-4">
              <div className="relative bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className="w-full h-64 object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="border-2 border-white border-dashed w-48 h-32 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">وجه الكاميرا نحو الباركود</span>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button onClick={stopCamera} variant="outline" className="flex-1">
                  <X className="mr-2 h-4 w-4" />
                  إلغاء
                </Button>
              </div>
              
              <div className="text-center text-sm text-muted-foreground">
                جاري البحث عن الباركود...
              </div>
            </div>
          )}

          {foundItem && (
            <div className="space-y-4">
              <div className="p-4 border rounded-lg bg-green-50 border-green-200">
                <div className="flex items-start gap-3">
                  <Package className="h-5 w-5 text-green-600 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-medium text-green-900">{foundItem.name}</h3>
                    <p className="text-sm text-green-700">الفئة: {foundItem.category}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary">
                        الكمية: {foundItem.quantity} {foundItem.unit}
                      </Badge>
                      {scannedCode && (
                        <Badge variant="outline">
                          الباركود: {scannedCode}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handleSelectItem} className="flex-1">
                  <Check className="mr-2 h-4 w-4" />
                  تحديد هذه المادة
                </Button>
                <Button onClick={handleReset} variant="outline">
                  مسح آخر
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Dialog wrapper for the barcode scanner
export function BarcodeScannerDialog({ 
  children, 
  onItemScanned 
}: { 
  children: React.ReactNode;
  onItemScanned?: (item: InventoryItem) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);

  const handleItemScanned = (item: InventoryItem) => {
    onItemScanned?.(item);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>مسح الباركود</DialogTitle>
          <DialogDescription>
            امسح الباركود للعثور على المادة في المخزون
          </DialogDescription>
        </DialogHeader>
        <BarcodeScanner 
          onItemScanned={handleItemScanned}
          onClose={() => setIsOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
