
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import LoginForm from "./components/login-form";
import { getTranslations } from "next-intl/server";
import { HardDrive } from "lucide-react";

export async function generateMetadata({params: {locale}}: {params: {locale: string}}) {
  const t = await getTranslations({locale, namespace: 'LoginPage'});
  return {
    title: t('title'),
  };
}

export default async function LoginPage() {
  const t = await getTranslations('LoginPage');
  const tNav = await getTranslations('Navigation');

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <div className="mb-8 flex items-center gap-2">
        <HardDrive className="h-10 w-10 text-primary" />
        <h1 className="text-3xl font-headline font-bold text-primary">{tNav('copperFlow')}</h1>
      </div>
      <Card className="w-full max-w-sm shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">{t('formTitle')}</CardTitle>
          <CardDescription>{t('formDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <LoginForm />
        </CardContent>
      </Card>
    </div>
  );
}
