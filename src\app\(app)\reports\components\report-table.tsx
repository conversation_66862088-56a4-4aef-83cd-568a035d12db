"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import type { Transaction, InventoryItem } from "@/types";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

interface ReportDataItem {
  itemId: string;
  itemName: string;
  category: InventoryItem['category'];
  unit: string;
  // startingQuantity: number; // This would require historical data or snapshots
  incoming: number;
  outgoing: number;
  consumption: number;
  // endingQuantity: number; // Calculated based on current stock or transactions
  currentQuantity: number;
}

interface ReportTableProps {
  data: ReportDataItem[];
}

function convertToCSV(data: ReportDataItem[]) {
  if (data.length === 0) return "";
  const headers = Object.keys(data[0]).join(',');
  const rows = data.map(row => 
    Object.values(row).map(value => 
      typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    ).join(',')
  );
  return [headers, ...rows].join('\n');
}

function downloadCSV(csvString: string, filename: string) {
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement("a");
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

export function ReportTable({ data }: ReportTableProps) {
  if (data.length === 0) {
    return <p className="text-center text-muted-foreground py-8">No data available for the selected filters.</p>;
  }

  const handleDownload = () => {
    const csvData = convertToCSV(data);
    downloadCSV(csvData, `inventory_report_${new Date().toISOString().split('T')[0]}.csv`);
  };

  return (
    <div>
      <div className="flex justify-end mb-4">
        <Button onClick={handleDownload}>
          <Download className="mr-2 h-4 w-4" /> Download CSV
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Item Name</TableHead>
            <TableHead>Category</TableHead>
            <TableHead className="text-right">Incoming</TableHead>
            <TableHead className="text-right">Outgoing</TableHead>
            <TableHead className="text-right">Consumption</TableHead>
            <TableHead className="text-right">Current Quantity</TableHead>
            <TableHead>Unit</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.itemId}>
              <TableCell className="font-medium">{item.itemName}</TableCell>
              <TableCell>{item.category}</TableCell>
              <TableCell className="text-right">{item.incoming}</TableCell>
              <TableCell className="text-right">{item.outgoing}</TableCell>
              <TableCell className="text-right">{item.consumption}</TableCell>
              <TableCell className="text-right font-semibold">{item.currentQuantity}</TableCell>
              <TableCell>{item.unit}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
