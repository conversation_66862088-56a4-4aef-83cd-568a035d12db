
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import type { InventoryItem, TransactionType } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useTransactionTypeTranslation, formatNumberArabic } from '@/lib/translations';

const formSchema = z.object({
  type: z.enum(["Incoming", "Outgoing", "Consumption"], { required_error: "Transaction type is required." }),
  itemId: z.string().min(1, "Material is required."),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1."),
  date: z.date({ required_error: "Date is required." }),
  description: z.string().optional(),
  reason: z.string().optional(),
  workOrder: z.string().optional(),
  personnelInvolved: z.string().optional(),
  location: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof formSchema>;

interface TransactionFormProps {
  inventoryItems: InventoryItem[];
  onSubmit: (data: TransactionFormValues) => Promise<void>;
  onClose?: () => void;
  defaultValues?: Partial<TransactionFormValues>;
}

export function TransactionForm({ inventoryItems, onSubmit, onClose, defaultValues }: TransactionFormProps) {
  const tForm = useTranslations('TransactionForm');
  const locale = useLocale();
  const translateTransactionType = useTransactionTypeTranslation();
  const { toast } = useToast();
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: defaultValues?.type || undefined,
      itemId: defaultValues?.itemId || undefined,
      quantity: defaultValues?.quantity !== undefined ? Number(defaultValues.quantity) : '',
      date: defaultValues?.date || undefined,
      description: defaultValues?.description || "",
      reason: defaultValues?.reason || "",
      workOrder: defaultValues?.workOrder || "",
      personnelInvolved: defaultValues?.personnelInvolved || "",
      location: defaultValues?.location || "",
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (form.getValues('date') === undefined) {
      form.setValue('date', new Date(), { shouldValidate: false, shouldDirty: false });
    }
  }, [form]);


  async function handleSubmit(data: TransactionFormValues) {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      const itemForToast = inventoryItems.find(item => item.id === data.itemId);
      toast({
        title: tForm('toastSuccessTitle'),
        description: tForm('toastSuccessDescription', { itemName: itemForToast?.name || data.itemId }),
      });
      form.reset({
        type: undefined,
        itemId: undefined,
        quantity: '',
        description: "",
        reason: "",
        workOrder: "",
        personnelInvolved: "",
        location: "",
        date: new Date()
      });
      if (onClose) onClose();
    } catch (error) {
      toast({
        title: tForm('toastErrorTitle'),
        description: tForm('toastErrorDescription', { details: (error instanceof Error ? ` ${error.message}` : '') }),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('transactionTypeLabel')}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={tForm('transactionTypePlaceholder')} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {(["Incoming", "Outgoing", "Consumption"] as TransactionType[]).map((type) => (
                      <SelectItem key={type} value={type}>{translateTransactionType(type)}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="itemId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('materialItemLabel')}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={tForm('materialItemPlaceholder')} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {inventoryItems.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        {item.name} ({formatNumberArabic(item.quantity, locale)} {item.unit})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('quantityLabel')}</FormLabel>
                <FormControl>
                  <Input type="number" placeholder={tForm('quantityPlaceholder')} {...field} onChange={e => field.onChange(e.target.value === '' ? '' : Number(e.target.value))} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{tForm('dateLabel')}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    {/* Removed FormControl wrapper from Button */}
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" />
                      {field.value ? format(field.value as Date, "PPP") : <span>{tForm('datePickerPlaceholder')}</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value as Date | undefined}
                      onSelect={field.onChange}
                      disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{tForm('descriptionLabel')}</FormLabel>
              <FormControl>
                <Textarea placeholder={tForm('descriptionPlaceholder')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('reasonLabel')}</FormLabel>
                <FormControl>
                  <Input placeholder={tForm('reasonPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="workOrder"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('workOrderLabel')}</FormLabel>
                <FormControl>
                  <Input placeholder={tForm('workOrderPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="personnelInvolved"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('personnelInvolvedLabel')}</FormLabel>
                <FormControl>
                  <Input placeholder={tForm('personnelInvolvedPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tForm('locationLabel')}</FormLabel>
                <FormControl>
                  <Input placeholder={tForm('locationPlaceholder')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2 pt-4">
          {onClose && <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>{tForm('cancelButton')}</Button>}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4 animate-spin" />}
            {isSubmitting ? tForm('submittingButton') : tForm('submitButton')}
          </Button>
        </div>
      </form>
    </Form>
  );
}
