
"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import type { Transaction } from "@/types"; // Assuming InventoryItem is also needed for item details
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { format } from 'date-fns';
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";

interface DetailedTransactionReportTableProps {
  transactions: Transaction[]; // Enriched transactions
}

function convertTransactionsToCSV(transactions: Transaction[], t: ReturnType<typeof useTranslations<string>>) {
  if (transactions.length === 0) return "";
  
  const headers = [
    t('tableDate'),
    t('tableType'),
    t('tableItemName'),
    t('tableItemCategory'),
    t('tableQuantity'),
    t('tableItemUnit'),
    t('tablePersonnel'),
    t('tableWorkOrder'),
    t('tableLocation'),
    t('tableDescription'),
    t('tableReason')
  ].join(',');

  const rows = transactions.map(txn => {
    const rowData = [
      format(new Date(txn.date), "yyyy-MM-dd HH:mm"),
      txn.type,
      `"${txn.itemName.replace(/"/g, '""')}"`, // Ensure item name is quoted if it contains commas
      txn.itemCategory || '', // Add itemCategory if available
      txn.quantity,
      txn.itemUnit || '', // Add itemUnit if available
      `"${(txn.personnelInvolved || '-').replace(/"/g, '""')}"`,
      `"${(txn.workOrder || '-').replace(/"/g, '""')}"`,
      `"${(txn.location || '-').replace(/"/g, '""')}"`,
      `"${(txn.description || '-').replace(/"/g, '""')}"`,
      `"${(txn.reason || '-').replace(/"/g, '""')}"`
    ];
    return rowData.join(',');
  });
  return [headers, ...rows].join('\n');
}

function downloadCSV(csvString: string, filename: string) {
  const blob = new Blob([`\uFEFF${csvString}`], { type: 'text/csv;charset=utf-8;' }); // Add BOM for Excel
  const link = document.createElement("a");
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

export function DetailedTransactionReportTable({ transactions }: DetailedTransactionReportTableProps) {
  const t = useTranslations('DetailedTransactionReport'); // Namespace for this component

  if (transactions.length === 0) {
    return <p className="text-center text-muted-foreground py-8">{t('noTransactionsFound')}</p>;
  }

  const handleDownload = () => {
    const csvData = convertTransactionsToCSV(transactions, t);
    downloadCSV(csvData, `detailed_transactions_report_${new Date().toISOString().split('T')[0]}.csv`);
  };

  const getTransactionTypeBadgeVariant = (type: Transaction['type']) => {
    switch (type) {
      case 'Incoming': return 'default';
      case 'Outgoing': return 'destructive';
      case 'Consumption': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <div>
      <div className="flex justify-end mb-4">
        <Button onClick={handleDownload}>
          <Download className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" /> {t('downloadCsvButton')}
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('tableDate')}</TableHead>
            <TableHead>{t('tableType')}</TableHead>
            <TableHead>{t('tableItemName')}</TableHead>
            <TableHead>{t('tableItemCategory')}</TableHead>
            <TableHead className="text-right rtl:text-left">{t('tableQuantity')}</TableHead>
            <TableHead>{t('tableItemUnit')}</TableHead>
            <TableHead>{t('tablePersonnel')}</TableHead>
            <TableHead>{t('tableWorkOrder')}</TableHead>
            <TableHead>{t('tableLocation')}</TableHead>
            <TableHead>{t('tableDescription')}</TableHead>
            <TableHead>{t('tableReason')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((txn) => (
            <TableRow key={txn.id}>
              <TableCell>{format(new Date(txn.date), "PPP p")}</TableCell>
              <TableCell><Badge variant={getTransactionTypeBadgeVariant(txn.type)}>{txn.type}</Badge></TableCell>
              <TableCell className="font-medium">{txn.itemName}</TableCell>
              <TableCell>{txn.itemCategory || '-'}</TableCell>
              <TableCell className="text-right rtl:text-left">{txn.quantity}</TableCell>
              <TableCell>{txn.itemUnit || '-'}</TableCell>
              <TableCell>{txn.personnelInvolved || '-'}</TableCell>
              <TableCell>{txn.workOrder || '-'}</TableCell>
              <TableCell>{txn.location || '-'}</TableCell>
              <TableCell>{txn.description || '-'}</TableCell>
              <TableCell>{txn.reason || '-'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
