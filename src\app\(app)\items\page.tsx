
"use client";

import { useEffect, useState, useCallback } from 'react';
import type { InventoryItem } from '@/types';
import { getInventoryItems, addInventoryItem as apiAddInventoryItem, updateInventoryItem as apiUpdateInventoryItem, deleteInventoryItem as apiDeleteInventoryItem } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { ItemForm, type ItemFormValues } from './components/item-form';
import { PlusCircle, Pencil, Trash2, Package, ThermometerSnowflake, ListTree, Loader2 } from 'lucide-react';
import PageHeader from '@/components/common/page-header';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { useToast } from "@/hooks/use-toast";

const categoryIcons: Record<InventoryItem['category'], JSX.Element> = {
  'Copper Cable': <Package className="h-5 w-5 text-primary" />,
  'FTTH Cable': <ThermometerSnowflake className="h-5 w-5 text-blue-500" />,
  'Equipment': <ListTree className="h-5 w-5 text-green-500" />,
  'Other': <Package className="h-5 w-5 text-gray-500" />,
};

export default function ItemsPage() {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);
  const [itemToDelete, setItemToDelete] = useState<InventoryItem | null>(null);
  const { toast } = useToast();

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const itemsData = await getInventoryItems();
      setItems(itemsData.sort((a,b) => a.name.localeCompare(b.name)));
    } catch (error) {
       toast({
        title: "Error fetching items",
        description: "Could not load inventory items.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleFormSubmit = async (data: ItemFormValues) => {
    try {
      if (editingItem) {
        await apiUpdateInventoryItem(editingItem.id, data);
        toast({ title: "Item Updated", description: `${data.name} has been updated.` });
      } else {
        await apiAddInventoryItem(data);
        toast({ title: "Item Added", description: `${data.name} has been added.` });
      }
      fetchData(); // Refetch to update the list
      setIsFormOpen(false);
      setEditingItem(null);
    } catch (error) {
      toast({
        title: "Error saving item",
        description: (error instanceof Error ? error.message : "Could not save item."),
        variant: "destructive",
      });
    }
  };

  const handleDeleteItem = async () => {
    if (!itemToDelete) return;
    try {
      await apiDeleteInventoryItem(itemToDelete.id);
      toast({ title: "Item Deleted", description: `${itemToDelete.name} has been deleted.` });
      fetchData(); // Refetch to update the list
      setItemToDelete(null);
    } catch (error) {
      toast({
        title: "Error deleting item",
        description: (error instanceof Error ? error.message : "Could not delete item."),
        variant: "destructive",
      });
    }
  };

  const openEditForm = (item: InventoryItem) => {
    setEditingItem(item);
    setIsFormOpen(true);
  };

  const openAddForm = () => {
    setEditingItem(null);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-8">
      <PageHeader title="Manage Inventory Items" description="Add, edit, or remove items from your inventory.">
        <Button onClick={openAddForm}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add New Item
        </Button>
      </PageHeader>

      <Dialog open={isFormOpen} onOpenChange={(isOpen) => {
        setIsFormOpen(isOpen);
        if (!isOpen) setEditingItem(null);
      }}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>{editingItem ? 'Edit Item' : 'Add New Item'}</DialogTitle>
            <DialogDescription>
              {editingItem ? 'Update the details of this inventory item.' : 'Fill in the details for the new inventory item.'}
            </DialogDescription>
          </DialogHeader>
          <ItemForm
            onSubmit={handleFormSubmit}
            defaultValues={editingItem || undefined}
            onClose={() => { setIsFormOpen(false); setEditingItem(null); }}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog open={!!itemToDelete} onOpenChange={(isOpen) => !isOpen && setItemToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the item "{itemToDelete?.name}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setItemToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteItem} className="bg-destructive hover:bg-destructive/90">Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Card>
        <CardHeader>
          <CardTitle>Inventory List</CardTitle>
          <CardDescription>All items currently in your inventory.</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : items.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">No items found. Click "Add New Item" to get started.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">Icon</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{categoryIcons[item.category] || <Package className="h-5 w-5 text-gray-400" />}</TableCell>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>
                      <Badge variant={item.category === 'Copper Cable' ? 'default' : item.category === 'FTTH Cable' ? 'secondary' : 'outline'}>
                        {item.category}
                      </Badge>
                    </TableCell>
                    <TableCell className={`text-right font-semibold ${item.quantity < 50 ? 'text-destructive' : ''}`}>
                      {item.quantity}
                    </TableCell>
                    <TableCell>{item.unit}</TableCell>
                    <TableCell>{format(new Date(item.lastUpdated), "PPP p")}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" onClick={() => openEditForm(item)} className="mr-2">
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">Edit {item.name}</span>
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => setItemToDelete(item)} className="text-destructive hover:text-destructive/90">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete {item.name}</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
