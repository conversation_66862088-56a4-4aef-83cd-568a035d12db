"use client";

import { useLocale } from 'next-intl';
import { formatDateArabic, formatDateShort, formatDateLong, formatTimeOnly, formatNumberArabic } from '@/lib/translations';

interface LocalizedNumberProps {
  value: number;
  className?: string;
}

export function LocalizedNumber({ value, className }: LocalizedNumberProps) {
  const locale = useLocale();

  return (
    <span className={className}>
      {formatNumberArabic(value, locale)}
    </span>
  );
}

interface LocalizedDateProps {
  value: string | Date;
  className?: string;
  format?: 'short' | 'long' | 'full' | 'time';
}

export function LocalizedDate({ value, className, format = 'full' }: LocalizedDateProps) {
  const locale = useLocale();

  let formattedDate: string;

  switch (format) {
    case 'short':
      formattedDate = formatDateShort(value, locale);
      break;
    case 'long':
      formattedDate = formatDateLong(value, locale);
      break;
    case 'time':
      formattedDate = formatTimeOnly(value, locale);
      break;
    case 'full':
    default:
      formattedDate = formatDateArabic(value, locale);
      break;
  }

  return (
    <span className={className}>
      {formattedDate}
    </span>
  );
}

interface LocalizedCurrencyProps {
  value: number;
  currency?: string;
  className?: string;
}

export function LocalizedCurrency({ value, currency = 'SAR', className }: LocalizedCurrencyProps) {
  const locale = useLocale();

  const formatted = new Intl.NumberFormat(locale === 'ar' ? 'ar-SA' : 'en-US', {
    style: 'currency',
    currency: currency,
  }).format(value);

  return (
    <span className={className}>
      {formatted}
    </span>
  );
}

interface LocalizedPercentageProps {
  value: number;
  className?: string;
  decimals?: number;
}

export function LocalizedPercentage({ value, className, decimals = 1 }: LocalizedPercentageProps) {
  const locale = useLocale();

  const formatted = new Intl.NumberFormat(locale === 'ar' ? 'ar-SA' : 'en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);

  return (
    <span className={className}>
      {formatted}
    </span>
  );
}
