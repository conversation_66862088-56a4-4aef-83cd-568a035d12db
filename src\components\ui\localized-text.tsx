"use client";

import { useLocale } from 'next-intl';
import { formatDateArabic, formatNumberArabic } from '@/lib/translations';

interface LocalizedNumberProps {
  value: number;
  className?: string;
}

export function LocalizedNumber({ value, className }: LocalizedNumberProps) {
  const locale = useLocale();
  
  return (
    <span className={className}>
      {formatNumberArabic(value, locale)}
    </span>
  );
}

interface LocalizedDateProps {
  value: string | Date;
  className?: string;
  format?: 'short' | 'long' | 'full';
}

export function LocalizedDate({ value, className, format = 'short' }: LocalizedDateProps) {
  const locale = useLocale();
  
  return (
    <span className={className}>
      {formatDateArabic(value, locale)}
    </span>
  );
}

interface LocalizedCurrencyProps {
  value: number;
  currency?: string;
  className?: string;
}

export function LocalizedCurrency({ value, currency = 'SAR', className }: LocalizedCurrencyProps) {
  const locale = useLocale();
  
  const formatted = new Intl.NumberFormat(locale === 'ar' ? 'ar-SA' : 'en-US', {
    style: 'currency',
    currency: currency,
  }).format(value);
  
  return (
    <span className={className}>
      {formatted}
    </span>
  );
}

interface LocalizedPercentageProps {
  value: number;
  className?: string;
  decimals?: number;
}

export function LocalizedPercentage({ value, className, decimals = 1 }: LocalizedPercentageProps) {
  const locale = useLocale();
  
  const formatted = new Intl.NumberFormat(locale === 'ar' ? 'ar-SA' : 'en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
  
  return (
    <span className={className}>
      {formatted}
    </span>
  );
}
