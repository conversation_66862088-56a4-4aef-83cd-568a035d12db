@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 180 60% 92.5%; /* Light Teal #E0F8F8 */
    --foreground: 180 25% 20%; /* Dark Tealish Gray */

    --muted: 180 40% 85%;
    --muted-foreground: 180 25% 35%;

    --popover: 180 60% 95%;
    --popover-foreground: 180 25% 20%;

    --card: 180 60% 97%; /* Slightly whiter than background */
    --card-foreground: 180 25% 20%;

    --border: 180 30% 80%;
    --input: 180 40% 90%;

    --primary: 180 100% 25.1%; /* Teal #008080 */
    --primary-foreground: 0 0% 100%; /* White */

    --secondary: 180 50% 70%; /* A lighter teal for secondary elements */
    --secondary-foreground: 180 100% 15%; /* Dark teal for text on secondary */

    --accent: 180 20% 54.1%; /* Light Blue #70A4A4 */
    --accent-foreground: 0 0% 100%; /* White */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --ring: 180 100% 25.1%; /* Teal for focus rings */
    --radius: 0.5rem;

    /* Sidebar specific theme variables */
    --sidebar-background: 180 35% 88%; 
    --sidebar-foreground: 180 25% 15%;
    --sidebar-primary: 180 100% 25.1%; 
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 180 30% 70%;
    --sidebar-accent-foreground: 180 100% 20%;
    --sidebar-border: 180 30% 75%;
    --sidebar-ring: 180 100% 25.1%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    /* Keeping default dark theme from shadcn, can be customized later if needed */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
