

export type UserRole = 'admin' | 'supervisor' | 'user';

export interface InventoryItem {
  id: string;
  name: string;
  quantity: number;
  category: 'Copper Cable' | 'FTTH Cable' | 'Equipment' | 'Other';
  unit: string; // e.g., meters, units, rolls
  lastUpdated: string; // ISO date string
}

export type TransactionType = 'Incoming' | 'Outgoing' | 'Consumption';

export interface Transaction {
  id: string;
  type: TransactionType;
  itemId: string;
  itemName: string; // For display convenience
  quantity: number;
  date: string; // ISO date string
  description?: string;
  reason?: string;
  workOrder?: string;
  personnelInvolved?: string; // Could be an array or ID later
  location?: string; // Added for reporting
  // Optional fields for enriched transaction data in reports
  itemCategory?: InventoryItem['category'];
  itemUnit?: string;
}

export interface ReportFilters {
  dateFrom?: Date;
  dateTo?: Date;
  location?: string;
  employee?: string;
}

export interface User {
  id: string;
  username?: string; 
  email: string;
  name?: string;
  role: User<PERSON>ole; 
}


