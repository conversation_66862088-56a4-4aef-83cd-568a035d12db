
"use client";

import { useEffect, useState, useCallback } from 'react';
import type { InventoryItem } from '@/types';
import { getInventoryItems, addInventoryItem as apiAddInventoryItem, updateInventoryItem as apiUpdateInventoryItem, deleteInventoryItem as apiDeleteInventoryItem } from '@/lib/data';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { ItemForm, type ItemFormValues } from './components/item-form';
import { PlusCircle, Pencil, Trash2, Package, ThermometerSnowflake, ListTree, Loader2 } from 'lucide-react';
import PageHeader from '@/components/common/page-header';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';

const categoryIcons: Record<InventoryItem['category'], JSX.Element> = {
  'Copper Cable': <Package className="h-5 w-5 text-primary" />,
  'FTTH Cable': <ThermometerSnowflake className="h-5 w-5 text-blue-500" />,
  'Equipment': <ListTree className="h-5 w-5 text-green-500" />,
  'Other': <Package className="h-5 w-5 text-gray-500" />,
};

export default function ItemsPage() {
  const t = useTranslations('ItemsPage');
  const tCommon = useTranslations('Common');
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);
  const [itemToDelete, setItemToDelete] = useState<InventoryItem | null>(null);
  const { toast } = useToast();

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const itemsData = await getInventoryItems();
      setItems(itemsData.sort((a,b) => a.name.localeCompare(b.name)));
    } catch (error) {
       toast({
        title: t('toast.errorFetching.title'),
        description: t('toast.errorFetching.description'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast, t]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleFormSubmit = async (data: ItemFormValues) => {
    try {
      if (editingItem) {
        await apiUpdateInventoryItem(editingItem.id, data);
        toast({ title: t('toast.itemUpdated.title'), description: t('toast.itemUpdated.description', { itemName: data.name }) });
      } else {
        await apiAddInventoryItem(data);
        toast({ title: t('toast.itemAdded.title'), description: t('toast.itemAdded.description', { itemName: data.name }) });
      }
      fetchData();
      setIsFormOpen(false);
      setEditingItem(null);
    } catch (error) {
      toast({
        title: t('toast.errorSaving.title'),
        description: (error instanceof Error ? error.message : t('toast.errorSaving.description')),
        variant: "destructive",
      });
    }
  };

  const handleDeleteItem = async () => {
    if (!itemToDelete) return;
    try {
      await apiDeleteInventoryItem(itemToDelete.id);
      toast({ title: t('toast.itemDeleted.title'), description: t('toast.itemDeleted.description', { itemName: itemToDelete.name }) });
      fetchData();
      setItemToDelete(null);
    } catch (error) {
      toast({
        title: t('toast.errorDeleting.title'),
        description: (error instanceof Error ? error.message : t('toast.errorDeleting.description')),
        variant: "destructive",
      });
    }
  };

  const openEditForm = (item: InventoryItem) => {
    setEditingItem(item);
    setIsFormOpen(true);
  };

  const openAddForm = () => {
    setEditingItem(null);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-8">
      <PageHeader title={t('title')} description={t('description')}>
        <Button onClick={openAddForm}>
          <PlusCircle className="mr-2 h-4 w-4 rtl:mr-0 rtl:ml-2" /> {t('addNewItemButton')}
        </Button>
      </PageHeader>

      <Dialog open={isFormOpen} onOpenChange={(isOpen) => {
        setIsFormOpen(isOpen);
        if (!isOpen) setEditingItem(null);
      }}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>{editingItem ? t('dialogEditTitle') : t('dialogAddTitle')}</DialogTitle>
            <DialogDescription>
              {editingItem ? t('dialogEditDescription') : t('dialogAddDescription')}
            </DialogDescription>
          </DialogHeader>
          <ItemForm
            onSubmit={handleFormSubmit}
            defaultValues={editingItem || undefined}
            onClose={() => { setIsFormOpen(false); setEditingItem(null); }}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog open={!!itemToDelete} onOpenChange={(isOpen) => !isOpen && setItemToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('alertDeleteTitle')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('alertDeleteDescription', { itemName: itemToDelete?.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setItemToDelete(null)}>{tCommon('cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteItem} className="bg-destructive hover:bg-destructive/90">{tCommon('delete')}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Card>
        <CardHeader>
          <CardTitle>{t('inventoryListTitle')}</CardTitle>
          <CardDescription>{t('inventoryListDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
             <div className="flex items-center justify-center h-64">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
             </div>
          ) : items.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">{t('noItemsFound')}</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">{t('tableIcon')}</TableHead>
                  <TableHead>{t('tableName')}</TableHead>
                  <TableHead>{t('tableCategory')}</TableHead>
                  <TableHead className="text-right rtl:text-left">{t('tableQuantity')}</TableHead>
                  <TableHead>{t('tableUnit')}</TableHead>
                  <TableHead>{t('tableLastUpdated')}</TableHead>
                  <TableHead className="text-right rtl:text-left w-[120px]">{t('tableActions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{categoryIcons[item.category] || <Package className="h-5 w-5 text-gray-400" />}</TableCell>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>
                      <Badge variant={item.category === 'Copper Cable' ? 'default' : item.category === 'FTTH Cable' ? 'secondary' : 'outline'}>
                        {item.category}
                      </Badge>
                    </TableCell>
                    <TableCell className={`text-right rtl:text-left font-semibold ${item.quantity < 50 ? 'text-destructive' : ''}`}>
                      {item.quantity}
                    </TableCell>
                    <TableCell>{item.unit}</TableCell>
                    <TableCell>{format(new Date(item.lastUpdated), "PPP p")}</TableCell>
                    <TableCell className="text-right rtl:text-left">
                      <Button variant="ghost" size="icon" onClick={() => openEditForm(item)} className="mr-2 rtl:mr-0 rtl:ml-2">
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">{t('editSrOnly', { itemName: item.name })}</span>
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => setItemToDelete(item)} className="text-destructive hover:text-destructive/90">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">{t('deleteSrOnly', { itemName: item.name })}</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
