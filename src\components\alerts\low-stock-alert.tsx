"use client";

import { useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Package, X, Settings } from 'lucide-react';
import type { InventoryItem } from '@/types';
import { getInventoryItems } from '@/lib/data';

interface LowStockSettings {
  threshold: number;
  enabled: boolean;
  criticalThreshold: number;
}

interface LowStockAlertProps {
  onDismiss?: (itemId: string) => void;
  settings?: LowStockSettings;
}

export function LowStockAlert({ onDismiss, settings = { threshold: 50, enabled: true, criticalThreshold: 10 } }: LowStockAlertProps) {
  const [lowStockItems, setLowStockItems] = useState<InventoryItem[]>([]);
  const [criticalStockItems, setCriticalStockItems] = useState<InventoryItem[]>([]);
  const [dismissedItems, setDismissedItems] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkStockLevels() {
      if (!settings.enabled) {
        setLoading(false);
        return;
      }

      try {
        const items = await getInventoryItems();
        
        const lowStock = items.filter(item => 
          item.quantity <= settings.threshold && 
          item.quantity > settings.criticalThreshold &&
          !dismissedItems.has(item.id)
        );
        
        const criticalStock = items.filter(item => 
          item.quantity <= settings.criticalThreshold &&
          !dismissedItems.has(item.id)
        );

        setLowStockItems(lowStock);
        setCriticalStockItems(criticalStock);
      } catch (error) {
        console.error('Error checking stock levels:', error);
      } finally {
        setLoading(false);
      }
    }

    checkStockLevels();
    
    // Check every 5 minutes
    const interval = setInterval(checkStockLevels, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [settings, dismissedItems]);

  const handleDismiss = (itemId: string) => {
    setDismissedItems(prev => new Set([...prev, itemId]));
    setLowStockItems(prev => prev.filter(item => item.id !== itemId));
    setCriticalStockItems(prev => prev.filter(item => item.id !== itemId));
    onDismiss?.(itemId);
  };

  if (loading || (!settings.enabled) || (lowStockItems.length === 0 && criticalStockItems.length === 0)) {
    return null;
  }

  return (
    <div className="space-y-4 mb-6">
      {/* Critical Stock Alerts */}
      {criticalStockItems.length > 0 && (
        <Alert variant="destructive" className="border-red-500 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle className="text-red-800">تنبيه مخزون حرج!</AlertTitle>
          <AlertDescription className="text-red-700">
            <div className="mt-2 space-y-2">
              <p>المواد التالية وصلت إلى مستوى حرج ويجب إعادة تعبئتها فوراً:</p>
              <div className="grid gap-2">
                {criticalStockItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between bg-white p-2 rounded border border-red-200">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-red-600" />
                      <span className="font-medium">{item.name}</span>
                      <Badge variant="destructive" className="text-xs">
                        {item.quantity} {item.unit}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDismiss(item.id)}
                      className="h-6 w-6 p-0 text-red-600 hover:text-red-800"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Low Stock Alerts */}
      {lowStockItems.length > 0 && (
        <Alert variant="default" className="border-yellow-500 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertTitle className="text-yellow-800">تنبيه مخزون منخفض</AlertTitle>
          <AlertDescription className="text-yellow-700">
            <div className="mt-2 space-y-2">
              <p>المواد التالية تحتاج إلى إعادة تعبئة قريباً:</p>
              <div className="grid gap-2">
                {lowStockItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between bg-white p-2 rounded border border-yellow-200">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium">{item.name}</span>
                      <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                        {item.quantity} {item.unit}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDismiss(item.id)}
                      className="h-6 w-6 p-0 text-yellow-600 hover:text-yellow-800"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// Settings component for configuring alert thresholds
export function LowStockSettings({ 
  settings, 
  onSettingsChange 
}: { 
  settings: LowStockSettings;
  onSettingsChange: (settings: LowStockSettings) => void;
}) {
  const [localSettings, setLocalSettings] = useState(settings);

  const handleSave = () => {
    onSettingsChange(localSettings);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          إعدادات تنبيهات المخزون
        </CardTitle>
        <CardDescription>
          قم بتخصيص حدود التنبيهات للمخزون المنخفض والحرج
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="enabled"
            checked={localSettings.enabled}
            onChange={(e) => setLocalSettings(prev => ({ ...prev, enabled: e.target.checked }))}
            className="rounded"
          />
          <label htmlFor="enabled" className="text-sm font-medium">
            تفعيل تنبيهات المخزون
          </label>
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">حد التنبيه للمخزون المنخفض</label>
          <input
            type="number"
            value={localSettings.threshold}
            onChange={(e) => setLocalSettings(prev => ({ ...prev, threshold: parseInt(e.target.value) || 0 }))}
            className="w-full p-2 border rounded"
            min="0"
          />
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">حد التنبيه للمخزون الحرج</label>
          <input
            type="number"
            value={localSettings.criticalThreshold}
            onChange={(e) => setLocalSettings(prev => ({ ...prev, criticalThreshold: parseInt(e.target.value) || 0 }))}
            className="w-full p-2 border rounded"
            min="0"
          />
        </div>
        
        <Button onClick={handleSave} className="w-full">
          حفظ الإعدادات
        </Button>
      </CardContent>
    </Card>
  );
}
