"use client";

import { useLocale } from 'next-intl';
import { useEffect } from 'react';

interface RTLProviderProps {
  children: React.ReactNode;
}

export function RTLProvider({ children }: RTLProviderProps) {
  const locale = useLocale();

  useEffect(() => {
    // Set document direction based on locale
    const direction = locale === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.dir = direction;
    document.documentElement.lang = locale;
    
    // Add locale class to body for CSS targeting
    document.body.classList.remove('locale-ar', 'locale-en');
    document.body.classList.add(`locale-${locale}`);
    
    // Add direction class to body
    document.body.classList.remove('dir-rtl', 'dir-ltr');
    document.body.classList.add(`dir-${direction}`);
  }, [locale]);

  return <>{children}</>;
}

// Hook to get current direction
export function useDirection() {
  const locale = useLocale();
  return locale === 'ar' ? 'rtl' : 'ltr';
}

// Hook to check if current locale is RTL
export function useIsRTL() {
  const locale = useLocale();
  return locale === 'ar';
}

// Utility function to get direction-aware classes
export function getDirectionClasses(locale: string) {
  const isRTL = locale === 'ar';
  
  return {
    textAlign: isRTL ? 'text-right' : 'text-left',
    marginLeft: isRTL ? 'mr-' : 'ml-',
    marginRight: isRTL ? 'ml-' : 'mr-',
    paddingLeft: isRTL ? 'pr-' : 'pl-',
    paddingRight: isRTL ? 'pl-' : 'pr-',
    borderLeft: isRTL ? 'border-r-' : 'border-l-',
    borderRight: isRTL ? 'border-l-' : 'border-r-',
    roundedLeft: isRTL ? 'rounded-r-' : 'rounded-l-',
    roundedRight: isRTL ? 'rounded-l-' : 'rounded-r-',
    flexDirection: isRTL ? 'flex-row-reverse' : 'flex-row',
    justifyContent: {
      start: isRTL ? 'justify-end' : 'justify-start',
      end: isRTL ? 'justify-start' : 'justify-end',
    }
  };
}

// Component for direction-aware spacing
interface DirectionalSpacingProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg';
  direction?: 'horizontal' | 'vertical';
}

export function DirectionalSpacing({ 
  children, 
  className = '', 
  spacing = 'md',
  direction = 'horizontal'
}: DirectionalSpacingProps) {
  const locale = useLocale();
  const isRTL = locale === 'ar';
  
  const spacingMap = {
    sm: '2',
    md: '4',
    lg: '6'
  };
  
  const spacingValue = spacingMap[spacing];
  
  let spacingClasses = '';
  if (direction === 'horizontal') {
    spacingClasses = isRTL 
      ? `space-x-reverse space-x-${spacingValue}` 
      : `space-x-${spacingValue}`;
  } else {
    spacingClasses = `space-y-${spacingValue}`;
  }
  
  return (
    <div className={`${spacingClasses} ${className}`}>
      {children}
    </div>
  );
}

// Component for direction-aware flex layout
interface DirectionalFlexProps {
  children: React.ReactNode;
  className?: string;
  reverse?: boolean;
}

export function DirectionalFlex({ 
  children, 
  className = '', 
  reverse = false 
}: DirectionalFlexProps) {
  const locale = useLocale();
  const isRTL = locale === 'ar';
  
  const shouldReverse = reverse ? !isRTL : isRTL;
  const flexDirection = shouldReverse ? 'flex-row-reverse' : 'flex-row';
  
  return (
    <div className={`flex ${flexDirection} ${className}`}>
      {children}
    </div>
  );
}
