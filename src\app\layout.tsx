
import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { fontClasses } from '@/lib/fonts';

// Note: Parameters for RootLayout are not needed when using next-intl for `locale`
// as it's handled by src/app/[locale]/layout.tsx

export const metadata: Metadata = {
  title: 'تدفق النحاس - نظام إدارة المخزون | Copper Flow - Inventory Management',
  description: 'نظام إدارة المخزون للكابلات النحاسية والألياف البصرية | Inventory management for copper and FTTH cables.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    // The lang and dir attributes will be set in src/app/[locale]/layout.tsx
    <html>
      <body className={`font-body antialiased ${fontClasses}`} suppressHydrationWarning={true}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
