
import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";

// Note: Parameters for RootLayout are not needed when using next-intl for `locale`
// as it's handled by src/app/[locale]/layout.tsx

export const metadata: Metadata = {
  title: 'تدفق النحاس - نظام إدارة المخزون | Copper Flow - Inventory Management',
  description: 'نظام إدارة المخزون للكابلات النحاسية والألياف البصرية | Inventory management for copper and FTTH cables.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    // The lang and dir attributes will be set in src/app/[locale]/layout.tsx
    <html>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=PT+Sans:ital,wght@0,400;0,700;1,400;1,700&family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet" />
      </head>
      <body className="font-body antialiased" suppressHydrationWarning={true}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
