
"use client";

import React from "react"; // Ensure React is imported
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Filter, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import type { ReportFilters as ReportFiltersType } from "@/types";
import { Input } from "@/components/ui/input"; // Keep for other fields like location

const reportFiltersSchema = z.object({
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  location: z.string().optional(),
  employee: z.string().optional(),
}).refine(data => {
    if (data.dateFrom && data.dateTo && data.dateFrom > data.dateTo) {
      return false;
    }
    return true;
  }, {
    message: "End date cannot be earlier than start date.",
    path: ["dateTo"],
  });

type ReportFiltersFormValues = z.infer<typeof reportFiltersSchema>;

interface ReportFiltersProps {
  onApplyFilters: (filters: ReportFiltersType) => void;
  onClearFilters: () => void;
  initialFilters?: ReportFiltersType;
}

export function ReportFilters({ onApplyFilters, onClearFilters, initialFilters }: ReportFiltersProps) {
  const form = useForm<ReportFiltersFormValues>({
    resolver: zodResolver(reportFiltersSchema),
    defaultValues: {
      dateFrom: initialFilters?.dateFrom,
      dateTo: initialFilters?.dateTo,
      location: initialFilters?.location || "",
      employee: initialFilters?.employee || "",
    },
  });

  function handleSubmit(data: ReportFiltersFormValues) {
    onApplyFilters(data);
  }

  function handleClear() {
    form.reset({ dateFrom: undefined, dateTo: undefined, location: "", employee: "" });
    onClearFilters();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 md:space-y-0 md:flex md:flex-wrap md:gap-4 md:items-end p-4 border rounded-lg shadow-sm bg-card mb-6">
        <FormField
          control={form.control}
          name="dateFrom"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>{"Date From"}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn("w-full md:w-[200px] pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                    >
                      {field.value ? format(field.value, "PPP") : <span>{"Pick a date"}</span>}
                      <CalendarIcon className="ml-auto rtl:ml-0 rtl:mr-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="dateTo"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>{"Date To"}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn("w-full md:w-[200px] pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                    >
                      {field.value ? format(field.value, "PPP") : <span>{"Pick a date"}</span>}
                      <CalendarIcon className="ml-auto rtl:ml-0 rtl:mr-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{"Location"}</FormLabel>
              <FormControl>
                <Input placeholder={"e.g., Warehouse A"} {...field} className="w-full md:w-[200px]" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="employee"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{"Employee"}</FormLabel>
              <FormControl>
                <input
                  type="text"
                  placeholder="e.g., John Doe"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm md:w-[200px]"
                  name={field.name}
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  // ref={field.ref} // Deliberately omitting ref for this test
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex gap-2 pt-4 md:pt-0">
          <Button type="submit">
            <Filter className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" /> {"Apply Filters"}
          </Button>
          <Button type="button" variant="outline" onClick={handleClear}>
            <X className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4" /> {"Clear"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
