
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import type { InventoryItem, TransactionType } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect } from 'react';

const formSchema = z.object({
  type: z.enum(["Incoming", "Outgoing", "Consumption"], { required_error: "Transaction type is required." }),
  itemId: z.string().min(1, "Material is required."),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1."),
  date: z.date({ required_error: "Date is required." }),
  description: z.string().optional(),
  reason: z.string().optional(),
  workOrder: z.string().optional(),
  personnelInvolved: z.string().optional(),
  location: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof formSchema>;

interface TransactionFormProps {
  inventoryItems: InventoryItem[];
  onSubmit: (data: TransactionFormValues) => Promise<void>;
  onClose?: () => void; // Optional: for closing a dialog
  defaultValues?: Partial<TransactionFormValues>;
}

export function TransactionForm({ inventoryItems, onSubmit, onClose, defaultValues }: TransactionFormProps) {
  const { toast } = useToast();
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: defaultValues?.type || undefined,
      itemId: defaultValues?.itemId || undefined,
      quantity: defaultValues?.quantity !== undefined ? Number(defaultValues.quantity) : '', // Ensure number or empty string
      date: defaultValues?.date || undefined,
      description: defaultValues?.description || "",
      reason: defaultValues?.reason || "",
      workOrder: defaultValues?.workOrder || "",
      personnelInvolved: defaultValues?.personnelInvolved || "",
      location: defaultValues?.location || "",
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Set date to new Date() on client-side if it's not already set by props or form state
  useEffect(() => {
    // Only set date if it's a new form (date is undefined)
    if (form.getValues('date') === undefined) {
      form.setValue('date', new Date(), { shouldValidate: false, shouldDirty: false });
    }
  }, [form]);


  async function handleSubmit(data: TransactionFormValues) {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      toast({
        title: "Transaction Successful",
        description: `Transaction for ${inventoryItems.find(item => item.id === data.itemId)?.name} recorded.`,
      });
      form.reset({ 
          type: undefined, 
          itemId: undefined, 
          quantity: '', // Reset quantity to empty string
          description: "", 
          reason: "", 
          workOrder: "", 
          personnelInvolved: "", 
          location: "",
          date: new Date() 
      }); 
      if (onClose) onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to record transaction. " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Transaction Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select transaction type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {(["Incoming", "Outgoing", "Consumption"] as TransactionType[]).map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="itemId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Material/Item</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an item" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {inventoryItems.map((item) => (
                      <SelectItem key={item.id} value={item.id}>{item.name} ({item.quantity} {item.unit})</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="Enter quantity" {...field} onChange={e => field.onChange(e.target.value === '' ? '' : Number(e.target.value))} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
           <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea placeholder="Brief description of the transaction" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reason (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Reason for transaction" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="workOrder"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Work Order (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., WO-12345" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
           <FormField
            control={form.control}
            name="personnelInvolved"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Personnel Involved (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Name of person(s)" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Main Warehouse, Site A" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2 pt-4">
            {onClose && <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>Cancel</Button>}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting ? "Submitting..." : "Submit Transaction"}
            </Button>
        </div>
      </form>
    </Form>
  );
}
